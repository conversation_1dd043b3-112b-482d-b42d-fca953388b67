import React from 'react';

const NewsletterTab = ({ footerData, setFooterData }) => {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-800">Newsletter Section</h2>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Newsletter Title</label>
          <input
            type="text"
            value={footerData?.newsletter?.title || ''}
            onChange={(e) => setFooterData(prev => ({
              ...prev,
              newsletter: { ...prev.newsletter, title: e.target.value }
            }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Newsletter section title"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Newsletter Description</label>
          <textarea
            value={footerData?.newsletter?.description || ''}
            onChange={(e) => setFooterData(prev => ({
              ...prev,
              newsletter: { ...prev.newsletter, description: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Newsletter description"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="newsletter-active"
            checked={footerData?.newsletter?.isActive || false}
            onChange={(e) => setFooterData(prev => ({
              ...prev,
              newsletter: { ...prev.newsletter, isActive: e.target.checked }
            }))}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="newsletter-active" className="ml-2 block text-sm text-gray-700">
            Show newsletter section in footer
          </label>
        </div>
      </div>
    </div>
  );
};

export default NewsletterTab;
