import React from 'react';
import { formatPrice } from '../../../../utils/currency';

const ProductTable = ({ products, loading, onEdit, onDelete }) => {
    if (loading) {
        return (
            <div className="bg-white rounded-xl shadow-lg">
                <div className="p-8 text-center">Loading products...</div>
            </div>
        );
    }

    return (
        <div className="bg-white rounded-xl shadow-lg overflow-x-auto">
            <div className="px-6 py-4 border-b">
                <h2 className="text-xl font-semibold text-gray-800">
                    Manage Products ({products.length})
                </h2>
            </div>
            <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                    <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Page</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pricing</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody className="bg-white divide-y">
                    {products.map((p) => (
                        <tr key={p._id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                    <img 
                                        src={p.images?.[0] || 'https://via.placeholder.com/100'} 
                                        alt={p.name} 
                                        className="w-14 h-14 rounded-md object-cover mr-4 bg-gray-100 flex-shrink-0"
                                    />
                                    <div className="text-sm font-medium text-gray-900">
                                        {p.name}
                                    </div>
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-600">
                                    {p.category?.name || <span className="text-gray-400 italic">N/A</span>}
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-600">
                                    {p.navLink?.title || <span className="text-gray-400 italic">N/A</span>}
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm">
                                    {p.pricing ? (
                                        <div className="space-y-1">
                                            {p.pricing.withoutRevision && (
                                                <div className="text-green-700 font-medium">
                                                    No Rev: {formatPrice(p.pricing.withoutRevision.price, p.currency)}
                                                </div>
                                            )}
                                            {p.pricing.withRevision && (
                                                <div className="text-blue-700">
                                                    With Rev: {formatPrice(p.pricing.withRevision.price, p.currency)}
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="text-gray-700">
                                            {formatPrice(p.price, p.currency)}
                                        </div>
                                    )}
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${p.isNewProduct ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                                    {p.isNewProduct ? 'New Arrival' : 'Standard'}
                                </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button 
                                    onClick={() => onEdit(p)} 
                                    className="text-indigo-600 hover:underline mr-4"
                                >
                                    Edit
                                </button>
                                <button 
                                    onClick={() => onDelete(p._id)} 
                                    className="text-red-600 hover:underline"
                                >
                                    Delete
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default ProductTable;
