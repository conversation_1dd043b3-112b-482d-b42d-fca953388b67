import React from 'react';
import { ImageIcon } from 'lucide-react';

const ImageUploadSlot = ({ preview, onSelect, index }) => (
    <div className="relative aspect-square bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 hover:border-indigo-500 transition-colors cursor-pointer overflow-hidden">
        <input
            type="file"
            onChange={(e) => onSelect(e, index)}
            accept="image/*"
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
        />
        {preview ? (
            <img src={preview} alt="Preview" className="w-full h-full object-cover" />
        ) : (
            <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                    <ImageIcon size={24} className="mx-auto text-gray-400" />
                    <p className="mt-1 text-xs text-gray-500">Upload Image</p>
                </div>
            </div>
        )}
    </div>
);

export default ImageUploadSlot;
