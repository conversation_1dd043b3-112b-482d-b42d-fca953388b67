import React, { useState, useEffect, useMemo } from 'react';
import axios from 'axios';
import { useLocation } from 'react-router-dom';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';

import ProductControls from './ProductControls';
import ProductGrid from './ProductGrid';
import FilterSidebar from './FilterSidebar';
import PricingSelector from '../../../../components/PricingSelector';

const ProductDisplayPage = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pageTitle, setPageTitle] = useState('Our Product Collection');

  const location = useLocation();
  const currentPath = location.pathname;

  const [searchTerm, setSearchTerm] = useState('');
  const [maxPrice, setMaxPrice] = useState(1000);
  const [sortOrder, setSortOrder] = useState('default');
  const [isFilterSidebarOpen, setIsFilterSidebarOpen] = useState(false);
  
  const [pageCategories, setPageCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const productsPerPage = 9;

  useEffect(() => {
    window.scrollTo(0, 0);
    const fetchPageData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Build API URLs
        const productApiUrl = selectedCategory
          ? `http://localhost:5000/api/products?categoryId=${selectedCategory._id}`
          : `http://localhost:5000/api/products?path=${currentPath}`;
        
        // Fetch categories specific to the current page
        const categoryApiUrl = `http://localhost:5000/api/categories?path=${currentPath}`;
        
        const [productRes, categoryRes] = await Promise.all([
          axios.get(productApiUrl),
          axios.get(categoryApiUrl)
        ]);

        setProducts(productRes.data);
        setPageCategories(categoryRes.data);
        
        // Set max price based on available products
        if (productRes.data.length > 0) {
          const highestPrice = Math.max(...productRes.data.map(p => p.price));
          setMaxPrice(Math.ceil(highestPrice));
        } else {
          setMaxPrice(1000);
        }

        // Set page title based on current path
        if (currentPath !== '/products') {
          const navLinkRes = await axios.get(`http://localhost:5000/api/navlinks?path=${currentPath}`);
          if (navLinkRes.data) setPageTitle(navLinkRes.data.title);
        } else {
          setPageTitle('All Products');
        }

      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Could not load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    fetchPageData();
  }, [currentPath, selectedCategory]);

  // Handle category change
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Memoize the products array after filtering and sorting
  const filteredAndSortedProducts = useMemo(() => {
    return products
      .filter(product => {
        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesPrice = product.price <= maxPrice;
        return matchesSearch && matchesPrice;
      })
      .sort((a, b) => {
        switch (sortOrder) {
          case 'price-asc':
            return a.price - b.price;
          case 'price-desc':
            return b.price - a.price;
          case 'name-asc':
            return a.name.localeCompare(b.name);
          case 'name-desc':
            return b.name.localeCompare(a.name);
          default:
            return 0;
        }
      });
  }, [products, searchTerm, maxPrice, sortOrder]);

  // Calculate pagination values
  const totalPages = Math.ceil(filteredAndSortedProducts.length / productsPerPage);
  const currentProducts = filteredAndSortedProducts.slice(
    (currentPage - 1) * productsPerPage,
    currentPage * productsPerPage
  );

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Generate page numbers
  const pageNumbers = [];
  for (let i = 1; i <= totalPages; i++) {
    pageNumbers.push(i);
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">{pageTitle}</h1>
          <p className="mt-4 max-w-2xl mx-auto text-lg text-gray-500">
            Browse our curated selection. Use the filters to find exactly what you're looking for.
          </p>
        </div>
        
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between mb-6">
          <ProductControls
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            sortOrder={sortOrder}
            onSortChange={setSortOrder}
            onFilterToggle={() => setIsFilterSidebarOpen(true)}
          />
          <PricingSelector showLabel={false} />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 lg:gap-12 relative">
          <FilterSidebar
            categories={pageCategories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
            maxPrice={maxPrice}
            priceRangeMax={Math.max(...products.map(p => p.price))}
            onPriceChange={setMaxPrice}
            isOpen={isFilterSidebarOpen}
            onClose={() => setIsFilterSidebarOpen(false)}
          />

          <div className="md:col-span-3">
            <ProductGrid 
              loading={loading}
              error={error}
              products={currentProducts}
            />
            
            {/* Pagination */}
            {!loading && !error && totalPages > 1 && (
              <div className="mt-8 flex justify-center gap-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft size={20} />
                </button>
                
                {pageNumbers.map(number => (
                  <button
                    key={number}
                    onClick={() => handlePageChange(number)}
                    className={`px-4 py-2 rounded-lg border ${
                      currentPage === number
                        ? 'bg-indigo-600 text-white border-indigo-600'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {number}
                  </button>
                ))}

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight size={20} />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDisplayPage;