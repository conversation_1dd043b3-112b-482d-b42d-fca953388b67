import { ShieldX, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

const AccessDenied = ({ 
  title = "Access Denied", 
  message = "Your role doesn't support access to this page",
  showBackButton = true,
  backTo = "/dashboard"
}) => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
          <div className="text-center">
            {/* Icon */}
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
              <ShieldX className="h-8 w-8 text-red-600" />
            </div>
            
            {/* Title */}
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {title}
            </h2>
            
            {/* Message */}
            <p className="text-gray-600 mb-8">
              {message}
            </p>
            
            {/* Action Buttons */}
            <div className="space-y-3">
              {showBackButton && (
                <Link
                  to={backTo}
                  className="w-full flex justify-center items-center gap-2 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
                >
                  <ArrowLeft size={16} />
                  Go Back
                </Link>
              )}
              
              <Link
                to="/"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
              >
                Return to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccessDenied;
