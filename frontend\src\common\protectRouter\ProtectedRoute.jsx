import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const ProtectedRoute = ({ requiredRole, requiredPermission, allowedRoles }) => {
  const { isLoggedIn, hasRole, hasAnyRole, hasPermission, canAccessDashboard } = useAuth();

  // If not logged in, redirect to login
  if (!isLoggedIn) {
    return <Navigate to="/login" replace />;
  }

  // If specific role is required
  if (requiredRole && !hasRole(requiredRole)) {
    return <Navigate to="/" replace />;
  }

  // If any of the allowed roles is required
  if (allowedRoles && !hasAnyRole(allowedRoles)) {
    return <Navigate to="/" replace />;
  }

  // If specific permission is required
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return <Navigate to="/" replace />;
  }

  // For dashboard routes, check if user can access dashboard
  if (window.location.pathname.includes('/dashboard') && !canAccessDashboard()) {
    return <Navigate to="/" replace />;
  }

  return <Outlet />;
};

// Component for dashboard access protection
export const DashboardProtectedRoute = () => {
  const { isLoggedIn, canAccessDashboard } = useAuth();

  if (!isLoggedIn) {
    return <Navigate to="/login" replace />;
  }

  if (!canAccessDashboard()) {
    return <Navigate to="/" replace />;
  }

  return <Outlet />;
};

// Component for admin-only routes
export const AdminProtectedRoute = () => {
  const { isLoggedIn, hasRole, canAccessDashboard } = useAuth();

  if (!isLoggedIn) {
    return <Navigate to="/login" replace />;
  }

  if (!hasRole('admin')) {
    // If user can access dashboard but is not admin, redirect to dashboard
    // If user is a buyer, redirect to home
    return <Navigate to={canAccessDashboard() ? "/dashboard" : "/"} replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;