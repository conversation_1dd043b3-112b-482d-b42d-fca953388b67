import React, { useState, useEffect } from 'react';
import { FaMapMarkerAlt } from 'react-icons/fa';
import HeroSection from './components/HeroSection';
import ContactForm from './components/ContactForm';
import ContactInfo from './components/ContactInfo';
import FAQSection from './components/FaqSection';
import MapSection from './components/MapSection';

const ContactUs = () => {
  const [contactUsData, setContactUsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Get current language from localStorage
  const getCurrentLanguage = () => {
    return localStorage.getItem('selectedLanguage') || 'fr';
  };

  // Fetch Contact Us data from API
  useEffect(() => {
    const fetchContactUsData = async () => {
      try {
        setLoading(true);
        setError('');

        const currentLang = getCurrentLanguage();
        const response = await fetch(`http://localhost:5000/api/contact-us?lang=${currentLang}`);

        if (!response.ok) {
          if (response.status === 404) {
            // No data found, use default data
            console.log('No Contact Us data found, using default data');
            setContactUsData(null);
          } else {
            throw new Error('Failed to fetch Contact Us data');
          }
        } else {
          const data = await response.json();
          setContactUsData(data);
        }
      } catch (err) {
        console.error('Error fetching Contact Us data:', err);
        setError(err.message);
        setContactUsData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchContactUsData();
  }, []);

  // Listen for language changes
  useEffect(() => {
    const handleLanguageChange = () => {
      const newLang = getCurrentLanguage();

      // Refetch data for new language
      const fetchData = async () => {
        try {
          const response = await fetch(`http://localhost:5000/api/contact-us?lang=${newLang}`);
          if (response.ok) {
            const data = await response.json();
            setContactUsData(data);
          }
        } catch (err) {
          console.error('Error refetching data for language change:', err);
        }
      };
      fetchData();
    };

    // Listen for storage changes and custom language change events
    window.addEventListener('storage', handleLanguageChange);
    window.addEventListener('languageChange', handleLanguageChange);

    return () => {
      window.removeEventListener('storage', handleLanguageChange);
      window.removeEventListener('languageChange', handleLanguageChange);
    };
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Contact Us content...</p>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {error && (
        <div className="bg-yellow-50 border border-yellow-200 p-4 text-center">
          <p className="text-yellow-800">Using default content due to: {error}</p>
        </div>
      )}

      {/* Hero Section */}
      <HeroSection />

      {/* Contact Form and Info Section */}
      <div className="container mx-auto px-4 py-16 mt-8 relative z-30">
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            <ContactInfo contactData={contactUsData?.contactInfo} />
            <ContactForm formData={contactUsData?.contactForm} />
          </div>
        </div>
      </div>

      <MapSection />

      {/* FAQ Section */}
      <FAQSection faqData={contactUsData?.faq} />
    </div>
  );
};

export default ContactUs;