import React from 'react';
import { Plus, Trash2 } from 'lucide-react';

const WhyShopWithUsTab = ({ 
  footerData, 
  setFooterData, 
  addFeature, 
  updateFeature, 
  removeFeature 
}) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Why Shop With Us Features</h2>
        <button
          onClick={addFeature}
          className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          <Plus size={16} />
          Add Feature
        </button>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">Section Title</label>
        <input
          type="text"
          value={footerData?.whyShopWithUs?.title || ''}
          onChange={(e) => setFooterData(prev => ({
            ...prev,
            whyShopWithUs: { ...prev.whyShopWithUs, title: e.target.value }
          }))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Section title"
        />
      </div>

      <div className="space-y-4">
        {footerData?.whyShopWithUs?.features?.map((feature, index) => (
          <div key={index} className="flex items-start gap-4 p-4 border border-gray-200 rounded-lg">
            <div className="flex-1 space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input
                  type="text"
                  value={feature.title}
                  onChange={(e) => updateFeature(index, 'title', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Feature title"
                />
                <input
                  type="text"
                  value={feature.icon}
                  onChange={(e) => updateFeature(index, 'icon', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Icon name (e.g., truck, shield)"
                />
              </div>
              <textarea
                value={feature.description}
                onChange={(e) => updateFeature(index, 'description', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Feature description"
              />
            </div>
            <button
              onClick={() => removeFeature(index)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
            >
              <Trash2 size={16} />
            </button>
          </div>
        ))}

        {(!footerData?.whyShopWithUs?.features || footerData.whyShopWithUs.features.length === 0) && (
          <div className="text-center py-8 text-gray-500">
            No features added yet. Click "Add Feature" to get started.
          </div>
        )}
      </div>
    </div>
  );
};

export default WhyShopWithUsTab;
