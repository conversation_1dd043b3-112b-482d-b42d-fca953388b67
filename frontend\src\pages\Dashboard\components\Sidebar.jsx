import React, { useState } from 'react';
import { NavLink, Link, Outlet } from 'react-router-dom';
import { LayoutDashboard, Images, Link as LinkIcon, ExternalLink, Settings, Info, Phone, Menu, X, Users } from 'lucide-react';
import {BiListCheck, BiCategoryAlt} from 'react-icons/bi'
import { FaBoxOpen } from 'react-icons/fa'
import { useAuth } from '../../../context/AuthContext';
import AccessDenied from '../../../common/AccessDenied/AccessDenied';

// Sidebar link data with permissions
const sidebarLinks = [
  { to: '/dashboard', icon: <LayoutDashboard size={18} />, text: 'Overview', permission: 'overview' },
  { to: '/dashboard/products', icon: <FaBoxOpen size={18} />, text: 'Create Products', permission: 'products' },
  { to: '/dashboard/slider', icon: <Images size={18} />, text: 'Manage Slider', permission: 'slider' },
  { to: '/dashboard/navbar', icon: <LinkIcon size={18} />, text: 'Manage Navbar', permission: 'navbar' },
  { to: '/dashboard/orders', icon: <BiListCheck size={18} />, text: 'Orders', permission: 'orders' },
  { to: '/dashboard/categories', icon: <BiCategoryAlt size={18} />, text: 'Categories', permission: 'categories' },
  { to: '/dashboard/about-us', icon: <Info size={18} />, text: 'About Us', permission: 'about-us' },
  { to: '/dashboard/contact-us', icon: <Phone size={18} />, text: 'Contact Us', permission: 'contact-us' },
  { to: '/dashboard/footer', icon: <Settings size={18} />, text: 'Footer Settings', permission: 'footer' },
  { to: '/admin', icon: <Users size={18} />, text: 'User Management', permission: 'users' },
];

// Reusable SidebarLink component
const SidebarLink = ({ to, icon, text, onClick, permission }) => {
  const { hasPermission } = useAuth();
  const activeClassName = "bg-indigo-600 text-white";
  const inactiveClassName = "text-gray-300 hover:bg-gray-700 hover:text-white";

  // Don't render if user doesn't have permission
  if (permission && !hasPermission(permission)) {
    return null;
  }

  return (
    <li>
      <NavLink
        to={to}
        end // Use 'end' for exact matching of the parent route
        onClick={() => onClick && onClick()}
        className={({ isActive }) =>
          `flex items-center space-x-3 py-2.5 px-4 rounded-md mx-2 transition-colors duration-200 ${
            isActive ? activeClassName : inactiveClassName
          }`
        }
      >
        {icon}
        <span className="font-medium">{text}</span>
      </NavLink>
    </li>
  );
};


const Sidebar = ({ isOpen, onClose }) => {
  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-gray-800 text-white flex flex-col transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="h-16 flex items-center justify-between px-4 border-b border-gray-700">
          <Link to="/dashboard" className="text-xl font-bold">Dashboard</Link>
          <button
            onClick={onClose}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <nav className="mt-6 flex-1 overflow-y-auto">
          <ul className="space-y-2">
            {sidebarLinks.map(link => (
              <SidebarLink key={link.to} {...link} onClick={onClose} />
            ))}
          </ul>
        </nav>

        {/* Footer link to view public site */}
        <div className="mt-auto border-t border-gray-700 p-2">
           <a
              href="/products"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-3 py-2.5 px-4 rounded-md mx-2 transition-colors duration-200 text-gray-400 hover:bg-gray-700 hover:text-white"
          >
              <ExternalLink size={18} />
              <span className="font-medium">View Public Site</span>
           </a>
        </div>
      </div>
    </>
  );
};

// Layout component that includes the sidebar and content area
const DashboardLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex h-screen bg-gray-100 font-sans">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile header */}
        <header className="lg:hidden bg-white shadow-sm border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-800">Dashboard</h1>
            <button
              onClick={() => setSidebarOpen(true)}
              className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              <Menu size={24} />
            </button>
          </div>
        </header>

        <main className="flex-1 overflow-y-auto">
          {/* The <Outlet> component will render the matched child route component */}
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;