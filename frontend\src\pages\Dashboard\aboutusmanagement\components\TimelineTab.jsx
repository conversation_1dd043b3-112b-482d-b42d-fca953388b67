import React, { useState } from 'react';
import { Plus, Trash2, Edit, Save, X } from 'lucide-react';

const TimelineTab = ({ data, onChange }) => {
  const [editingIndex, setEditingIndex] = useState(null);
  const [editingData, setEditingData] = useState({});

  const addTimelineItem = () => {
    const currentTimeline = data.timeline || [];
    const newItem = {
      year: new Date().getFullYear().toString(),
      title: { fr: '', ar: '' },
      description: { fr: '', ar: '' },
      milestones: { fr: [], ar: [] }
    };

    onChange('timeline', [...currentTimeline, newItem]);
  };

  const removeTimelineItem = (index) => {
    const currentTimeline = data.timeline || [];
    const newTimeline = currentTimeline.filter((_, i) => i !== index);
    onChange('timeline', newTimeline);
  };

  const startEditing = (index) => {
    setEditingIndex(index);
    setEditingData({ ...data.timeline[index] });
  };

  const saveEditing = () => {
    const currentTimeline = [...(data.timeline || [])];
    currentTimeline[editingIndex] = editingData;
    onChange('timeline', currentTimeline);
    setEditingIndex(null);
    setEditingData({});
  };

  const cancelEditing = () => {
    setEditingIndex(null);
    setEditingData({});
  };

  const handleEditingChange = (field, value, lang = 'fr') => {
    if (field === 'year') {
      setEditingData({ ...editingData, year: value });
    } else {
      setEditingData({
        ...editingData,
        [field]: {
          ...editingData[field],
          [lang]: value
        }
      });
    }
  };

  const addMilestone = (lang = 'fr') => {
    const currentMilestonesFr = editingData.milestones?.fr || [];
    const currentMilestonesAr = editingData.milestones?.ar || [];

    setEditingData({
      ...editingData,
      milestones: {
        fr: lang === 'fr' ? [...currentMilestonesFr, ''] : currentMilestonesFr,
        ar: lang === 'ar' ? [...currentMilestonesAr, ''] : currentMilestonesAr
      }
    });
  };

  const updateMilestone = (index, value, lang = 'fr') => {
    const currentMilestones = editingData.milestones?.[lang] || [];
    const newMilestones = [...currentMilestones];
    newMilestones[index] = value;

    setEditingData({
      ...editingData,
      milestones: {
        ...editingData.milestones,
        [lang]: newMilestones
      }
    });
  };

  const removeMilestone = (index, lang = 'fr') => {
    const currentMilestones = editingData.milestones?.[lang] || [];
    const newMilestones = currentMilestones.filter((_, i) => i !== index);

    setEditingData({
      ...editingData,
      milestones: {
        ...editingData.milestones,
        [lang]: newMilestones
      }
    });
  };

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          📅 Timeline Management
        </h2>
        <p className="text-gray-600">
          Manage the company timeline and milestones
        </p>
      </div>

      {/* Add New Timeline Item */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800">
            Timeline Items
          </h3>
          <button
            onClick={addTimelineItem}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Plus size={16} />
            Add Timeline Item
          </button>
        </div>

        {/* Timeline Items List */}
        <div className="space-y-6">
          {(data.timeline || []).map((item, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6">
              {editingIndex === index ? (
                /* Editing Mode */
                <div className="space-y-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-800">Edit Timeline Item</h4>
                    <div className="flex gap-2">
                      <button
                        onClick={saveEditing}
                        className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <Save size={16} />
                        Save
                      </button>
                      <button
                        onClick={cancelEditing}
                        className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <X size={16} />
                        Cancel
                      </button>
                    </div>
                  </div>

                  {/* Year */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Year</label>
                    <input
                      type="text"
                      value={editingData.year || ''}
                      onChange={(e) => handleEditingChange('year', e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="e.g., 2024"
                    />
                  </div>

                  {/* Title */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">Title</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                        <input
                          type="text"
                          value={editingData.title?.fr || ''}
                          onChange={(e) => handleEditingChange('title', e.target.value, 'fr')}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent focus:outline-none"
                          placeholder="Timeline title in French..."
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                        <input
                          type="text"
                          value={editingData.title?.ar || ''}
                          onChange={(e) => handleEditingChange('title', e.target.value, 'ar')}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent focus:outline-none text-right"
                          placeholder="عنوان الجدول الزمني بالعربية..."
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">Description</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                        <textarea
                          value={editingData.description?.fr || ''}
                          onChange={(e) => handleEditingChange('description', e.target.value, 'fr')}
                          rows={3}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent focus:outline-none"
                          placeholder="Timeline description in French..."
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                        <textarea
                          value={editingData.description?.ar || ''}
                          onChange={(e) => handleEditingChange('description', e.target.value, 'ar')}
                          rows={3}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent focus:outline-none text-right"
                          placeholder="وصف الجدول الزمني بالعربية..."
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Milestones */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <label className="block text-sm font-medium text-gray-700">Milestones</label>
                      <div className="flex gap-2">
                        <button
                          onClick={() => addMilestone('fr')}
                          className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm"
                        >
                          <Plus size={14} />
                          Add French
                        </button>
                        <button
                          onClick={() => addMilestone('ar')}
                          className="flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm"
                        >
                          <Plus size={14} />
                          Add Arabic
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* French Milestones */}
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇫🇷 French Milestones</label>
                        <div className="space-y-2">
                          {(editingData.milestones?.fr || []).map((milestone, mIndex) => (
                            <div key={`fr-${mIndex}`} className="flex items-center gap-2">
                              <input
                                type="text"
                                value={milestone}
                                onChange={(e) => updateMilestone(mIndex, e.target.value, 'fr')}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent focus:outline-none text-sm"
                                placeholder={`Milestone ${mIndex + 1} in French...`}
                              />
                              <button
                                onClick={() => removeMilestone(mIndex, 'fr')}
                                className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                              >
                                <Trash2 size={12} />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Arabic Milestones */}
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic Milestones</label>
                        <div className="space-y-2">
                          {(editingData.milestones?.ar || []).map((milestone, mIndex) => (
                            <div key={`ar-${mIndex}`} className="flex items-center gap-2">
                              <input
                                type="text"
                                value={milestone}
                                onChange={(e) => updateMilestone(mIndex, e.target.value, 'ar')}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent focus:outline-none text-sm text-right"
                                placeholder={`المعلم ${mIndex + 1} بالعربية...`}
                                dir="rtl"
                              />
                              <button
                                onClick={() => removeMilestone(mIndex, 'ar')}
                                className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                              >
                                <Trash2 size={12} />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                /* Display Mode */
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-bold text-sm">
                        {item.year}
                      </div>
                      <h4 className="text-lg font-semibold text-gray-800">
                        {item.title?.fr || 'Untitled'}
                      </h4>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => startEditing(index)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => removeTimelineItem(index)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-3">
                    {item.description?.fr || 'No description'}
                  </p>

                  {item.milestones?.fr && item.milestones.fr.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Milestones:</h5>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                        {item.milestones.fr.map((milestone, mIndex) => (
                          <li key={mIndex}>{milestone}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}

          {(!data.timeline || data.timeline.length === 0) && (
            <div className="text-center py-12 text-gray-500">
              <p className="text-lg mb-2">No timeline items yet</p>
              <p className="text-sm">Click "Add Timeline Item" to get started</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TimelineTab;
