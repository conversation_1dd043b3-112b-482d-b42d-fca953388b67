import express from 'express';
import {
  getFooterConfig,
  getFooterConfigForAdmin,
  updateFooterConfig,
  addSocialMediaLink,
  updateSocialMediaLink,
  deleteSocialMediaLink,
  addFooterSection,
  updateFooterSection,
  deleteFooterSection,
  addLegalLink,
  updateLegalLink,
  deleteLegalLink
} from '../controllers/footerController.js';
import protect from '../middleware/authMiddleware.js';
import { requirePermission } from '../middleware/roleMiddleware.js';

const router = express.Router();

// Public routes
router.get('/', getFooterConfig);

// Admin routes
router.get('/admin', protect, requirePermission('footer'), getFooterConfigForAdmin);
router.put('/', protect, requirePermission('footer'), updateFooterConfig);

// Social media routes
router.post('/social', protect, requirePermission('footer'), addSocialMediaLink);
router.put('/social/:id', protect, requirePermission('footer'), updateSocialMediaLink);
router.delete('/social/:id', protect, requirePermission('footer'), deleteSocialMediaLink);

// Footer sections routes
router.post('/sections', protect, requirePermission('footer'), addFooterSection);
router.put('/sections/:id', protect, requirePermission('footer'), updateFooterSection);
router.delete('/sections/:id', protect, requirePermission('footer'), deleteFooterSection);

// Legal links routes
router.post('/legal', protect, requirePermission('footer'), addLegalLink);
router.put('/legal/:id', protect, requirePermission('footer'), updateLegalLink);
router.delete('/legal/:id', protect, requirePermission('footer'), deleteLegalLink);

export default router;
