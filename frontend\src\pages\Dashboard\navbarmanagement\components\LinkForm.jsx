import React from 'react';

const LinkForm = ({ currentLink, setCurrentLink, isEditing, resetForm, handleSubmit, allLinks }) => {
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    // Special handling for path input
    if (name === 'path') {
      // Automatically format the path: trim spaces, remove multiple slashes
      const formattedPath = value.trim().replace(/\/+/g, '/');
      setCurrentLink(prev => ({ ...prev, [name]: formattedPath }));
      return;
    }
    
    setCurrentLink(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
  };

  // Filter out links that are not dropdowns and the current link itself
  const potentialParents = allLinks.filter(link => link.isDropdown && link._id !== currentLink?._id);

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 sticky top-6">
      <h2 className="text-xl font-semibold mb-4">{isEditing ? 'Edit Navigation Item' : 'Add Navigation Item'}</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium">Title</label>
          <input 
            type="text" 
            name="title" 
            value={currentLink.title || ''}
            onChange={handleInputChange}
            className="mt-1 w-full px-3 py-2 border rounded-lg focus:ring-indigo-500 focus:border-indigo-500" 
            required
            placeholder="Enter title"
          />
        </div>

        <div>
          <label className="flex items-center space-x-2">
            <input 
              type="checkbox" 
              name="isDropdown" 
              checked={currentLink.isDropdown || false}
              onChange={handleInputChange}
              className="rounded text-indigo-600 focus:ring-indigo-500 h-4 w-4" 
            />
            <span className="text-sm font-medium">Is this a dropdown container?</span>
          </label>
        </div>

        {!currentLink.isDropdown && (
          <div>
            <label className="block text-sm font-medium">Path</label>
            <div className="relative mt-1">
              <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">/</span>
              <input 
                type="text" 
                name="path" 
                value={currentLink.path?.replace(/^\//, '') || ''}
                onChange={handleInputChange}
                className="w-full pl-6 pr-3 py-2 border rounded-lg focus:ring-indigo-500 focus:border-indigo-500" 
                required={!currentLink.isDropdown}
                placeholder="Enter path without leading slash"
              />
            </div>
            <p className="mt-1 text-sm text-gray-500">
              Example: products/electronics
            </p>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium">Parent (optional)</label>
          <select 
            name="parentId" 
            value={currentLink.parentId || ''}
            onChange={handleInputChange}
            className="mt-1 w-full px-3 py-2 border rounded-lg bg-white focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="">None (Top-level)</option>
            {potentialParents.map(link => (
              <option key={link._id} value={link._id}>{link.title}</option>
            ))}
          </select>
        </div>

        <div className="flex items-center gap-4 pt-2">
          <button 
            type="submit" 
            className="px-6 py-2 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700"
          >
            {isEditing ? 'Update Item' : 'Add Item'}
          </button>
          {isEditing && (
            <button 
              type="button" 
              onClick={resetForm} 
              className="px-4 py-2 bg-gray-200 text-sm rounded-lg hover:bg-gray-300"
            >
              Cancel Edit
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default LinkForm;
