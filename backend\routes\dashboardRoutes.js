import express from 'express';
import { getDashboardStats } from '../controllers/dashboardController.js';
import protect from '../middleware/authMiddleware.js';
import { requirePermission } from '../middleware/roleMiddleware.js';
const router = express.Router();

// Route to get dashboard stats
router.route('/stats').get(protect, requirePermission('overview'), getDashboardStats);

export default router;