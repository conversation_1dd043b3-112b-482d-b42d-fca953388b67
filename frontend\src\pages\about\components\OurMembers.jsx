import React from 'react';

const OurMembers = ({ membersData }) => {
  // Use default values if membersData is not provided
  const {
    title = "Our Team",
    subtitle = "Meet the amazing people behind our success",
    members = []
  } = membersData || {};

  // Get current language from localStorage
  const getCurrentLanguage = () => {
    return localStorage.getItem('selectedLanguage') || 'fr';
  };

  const currentLang = getCurrentLanguage();
  const isRTL = currentLang === 'ar';

  if (!members || members.length === 0) {
    return null; // Don't render the section if no members
  }

  return (
    <div className={`py-20 px-4 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            {title}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {subtitle}
          </p>
        </div>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {members.map((member, index) => (
            <div 
              key={index} 
              className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* Member Image */}
              <div className="relative h-64 bg-gradient-to-br from-blue-100 to-indigo-100">
                {member.image ? (
                  <img 
                    src={member.image} 
                    alt={member.name || 'Team member'} 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="w-24 h-24 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-gray-500 text-4xl">👤</span>
                    </div>
                  </div>
                )}
                
                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>

              {/* Member Info */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {member.name || 'Team Member'}
                </h3>
                
                <p className="text-blue-600 font-semibold mb-3">
                  {member.position || 'Position'}
                </p>
                
                {member.description && (
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {member.description}
                  </p>
                )}
              </div>

              {/* Decorative Element */}
              <div className="h-1 bg-gradient-to-r from-blue-500 to-indigo-500"></div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {isRTL ? 'انضم إلى فريقنا' : 'Rejoignez notre équipe'}
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              {isRTL 
                ? 'نحن نبحث دائماً عن أشخاص موهوبين ومتحمسين للانضمام إلى فريقنا المتنامي.'
                : 'Nous recherchons toujours des personnes talentueuses et passionnées pour rejoindre notre équipe en croissance.'
              }
            </p>
            <button className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-3 rounded-full font-bold hover:from-blue-700 hover:to-indigo-700 transition-all shadow-lg hover:shadow-xl">
              {isRTL ? 'تواصل معنا' : 'Contactez-nous'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OurMembers;
