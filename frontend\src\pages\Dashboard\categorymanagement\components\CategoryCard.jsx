import React from 'react';
import { Trash2, ArrowUpRight } from 'lucide-react';
import { motion } from 'framer-motion';

const CategoryCard = ({ category, onDelete }) => {
  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      whileHover={{ y: -5 }}
      className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
    >
      <div className="aspect-video w-full bg-gray-100 relative overflow-hidden">
        {category.imageUrl ? (
          <img 
            src={category.imageUrl} 
            alt={category.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            <ArrowUpRight size={40} />
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">{category.name}</h3>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">
            {category.productCount} {category.productCount === 1 ? 'product' : 'products'}
          </span>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onDelete(category._id)}
              className="p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors"
              title="Delete category"
            >
              <Trash2 size={18} />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default CategoryCard;
