import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import connectDB from './config/db.js';
import productRoutes from './routes/productRoutes.js';
import authRoutes from './routes/authRoutes.js';
import slideRoutes from './routes/slideRoutes.js';
import navLinkRoutes from './routes/navLinkRoutes.js';
import orderRoutes from './routes/orderRoutes.js';
import dashboardRoutes from './routes/dashboardRoutes.js';
import categoryRoutes from './routes/categoryRoutes.js';
// --- NEW IMPORT ---
import contactRoutes from './routes/contactRoutes.js';
import footerRoutes from './routes/footerRoutes.js';
import aboutUsRoutes from './routes/aboutUs.js';
import contactUsRoutes from './routes/contactUs.js';
import userRoutes from './routes/userRoutes.js';

// --- Database Connection ---
connectDB();

const app = express();
const port = process.env.PORT || 5000;

// --- Middleware ---
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));


// --- API Routes ---
app.use('/api/products', productRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/slides', slideRoutes);
app.use('/api/navlinks', navLinkRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/categories', categoryRoutes);
// --- ADD NEW ROUTE ---
app.use('/api/contact', contactRoutes);
app.use('/api/footer', footerRoutes);
app.use('/api/about-us', aboutUsRoutes);
app.use('/api/contact-us', contactUsRoutes);
app.use('/api/users', userRoutes);

// --- Basic Error Handling ---
app.use((req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  res.status(404);
  next(error);
});

app.use((err, req, res, next) => {
  if (err.type === 'entity.too.large') {
    return res.status(413).json({ message: 'Request payload is too large.' });
  }

  const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
  res.status(statusCode);
  res.json({
    message: err.message,
    stack: process.env.NODE_ENV === 'production' ? null : err.stack,
  });
});

// --- Start Server ---
app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});