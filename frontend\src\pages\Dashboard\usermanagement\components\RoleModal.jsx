import { useState } from 'react';
import { X, Crown, Shield, Eye, ShoppingCart, Settings, Check } from 'lucide-react';

const RoleModal = ({ user, isOpen, onClose, onUpdateRole, updating }) => {
  const [selectedRole, setSelectedRole] = useState(user.role);

  const roles = [
    {
      value: 'buyer',
      label: 'Buyer',
      icon: <ShoppingCart className="h-5 w-5" />,
      description: 'Can only purchase products on the frontend. No dashboard access.',
      permissions: ['Purchase products'],
      color: 'text-gray-600'
    },
    {
      value: 'checker',
      label: 'Checker',
      icon: <Eye className="h-5 w-5" />,
      description: 'Can confirm and manage orders. Limited dashboard access.',
      permissions: ['View orders', 'Confirm orders', 'Dashboard overview'],
      color: 'text-green-600'
    },
    {
      value: 'creator',
      label: 'Creator',
      icon: <Shield className="h-5 w-5" />,
      description: 'Can create products, categories, and manage navbar links.',
      permissions: ['Create products', 'Manage categories', 'Manage navbar', 'Dashboard overview'],
      color: 'text-blue-600'
    },
    {
      value: 'controller',
      label: 'Controller',
      icon: <Settings className="h-5 w-5" />,
      description: 'Manages content pages like Footer, About Us, Contact Us.',
      permissions: ['Footer management', 'About Us management', 'Contact Us management', 'Dashboard overview'],
      color: 'text-purple-600'
    },
    {
      value: 'admin',
      label: 'Admin',
      icon: <Crown className="h-5 w-5" />,
      description: 'Full access to all dashboard pages and user management.',
      permissions: ['All permissions', 'User management', 'Full dashboard access'],
      color: 'text-yellow-600'
    }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (selectedRole !== user.role) {
      onUpdateRole(user._id, selectedRole);
    } else {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Manage User Role</h2>
            <p className="text-gray-600 mt-1">
              Update role for <span className="font-medium">{user.name}</span>
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={updating}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {roles.map((role) => (
              <div
                key={role.value}
                className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                  selectedRole === role.value
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedRole(role.value)}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-1">
                    <input
                      type="radio"
                      name="role"
                      value={role.value}
                      checked={selectedRole === role.value}
                      onChange={(e) => setSelectedRole(e.target.value)}
                      className="sr-only"
                    />
                    <div className={`${role.color}`}>
                      {role.icon}
                    </div>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-medium text-gray-900">{role.label}</h3>
                      {selectedRole === role.value && (
                        <Check className="h-4 w-4 text-indigo-600" />
                      )}
                    </div>
                    
                    <p className="text-gray-600 text-sm mb-3">
                      {role.description}
                    </p>
                    
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                        Permissions:
                      </p>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {role.permissions.map((permission, index) => (
                          <li key={index} className="flex items-center gap-1">
                            <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                            {permission}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
              disabled={updating}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={updating || selectedRole === user.role}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              {updating ? 'Updating...' : 'Update Role'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RoleModal;
