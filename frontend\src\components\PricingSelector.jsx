import React from 'react';
import { Edit, CreditCard } from 'lucide-react';
import { usePricing } from '../context/PricingContext';

const PricingSelector = ({ className = '', showLabel = true }) => {
  const { selectedPricingOption, updatePricingOption } = usePricing();

  const options = [
    {
      id: 'without-revision',
      label: 'Without Revision',
      shortLabel: 'No Revision',
      icon: <CreditCard size={16} />,
      description: 'Final sale - no changes',
      color: 'text-green-600 bg-green-50 border-green-200'
    },
    {
      id: 'with-revision',
      label: 'With Revision',
      shortLabel: 'With Revision',
      icon: <Edit size={16} />,
      description: 'Includes revision requests',
      color: 'text-blue-600 bg-blue-50 border-blue-200'
    }
  ];

  return (
    <div className={`pricing-selector ${className}`}>
      {showLabel && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Pricing Option
        </label>
      )}
      
      <div className="flex gap-2">
        {options.map((option) => (
          <button
            key={option.id}
            onClick={() => updatePricingOption(option.id)}
            className={`
              flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200 text-sm font-medium
              ${selectedPricingOption === option.id 
                ? option.color + ' border-current' 
                : 'text-gray-600 bg-white border-gray-300 hover:bg-gray-50'
              }
            `}
            title={option.description}
          >
            {option.icon}
            <span className="hidden sm:inline">{option.label}</span>
            <span className="sm:hidden">{option.shortLabel}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default PricingSelector;
