import User from '../models/User.js';

// Role permissions mapping
const ROLE_PERMISSIONS = {
  buyer: [],
  checker: ['orders', 'overview'],
  creator: ['overview', 'products', 'categories', 'navbar'],
  controller: ['overview', 'footer', 'about-us', 'contact-us'],
  admin: ['overview', 'products', 'categories', 'navbar', 'orders', 'footer', 'about-us', 'contact-us', 'slider', 'users']
};

// Middleware to check if user has required role
export const requireRole = (allowedRoles) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const user = await User.findById(req.user._id);
      if (!user) {
        return res.status(401).json({ message: 'User not found' });
      }

      // Check if user's role is in the allowed roles array
      if (!allowedRoles.includes(user.role)) {
        return res.status(403).json({ 
          message: 'Access denied. Your role does not have permission to access this resource.' 
        });
      }

      next();
    } catch (error) {
      console.error('Role middleware error:', error);
      return res.status(500).json({ message: 'Server error during role verification' });
    }
  };
};

// Middleware to check if user has permission for specific dashboard page
export const requirePermission = (permission) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const user = await User.findById(req.user._id);
      if (!user) {
        return res.status(401).json({ message: 'User not found' });
      }

      // Check if user's role has the required permission
      const userPermissions = ROLE_PERMISSIONS[user.role] || [];
      if (!userPermissions.includes(permission)) {
        return res.status(403).json({ 
          message: 'Your role doesn\'t support access to this page' 
        });
      }

      next();
    } catch (error) {
      console.error('Permission middleware error:', error);
      return res.status(500).json({ message: 'Server error during permission verification' });
    }
  };
};

// Middleware to ensure only admin can access certain routes
export const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    if (user.role !== 'admin') {
      return res.status(403).json({ 
        message: 'Access denied. Admin privileges required.' 
      });
    }

    next();
  } catch (error) {
    console.error('Admin middleware error:', error);
    return res.status(500).json({ message: 'Server error during admin verification' });
  }
};

// Middleware to check if user can access dashboard at all
export const requireDashboardAccess = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const user = await User.findById(req.user._id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Buyers cannot access dashboard
    if (user.role === 'buyer') {
      return res.status(403).json({ 
        message: 'Access denied. Dashboard access not available for your role.' 
      });
    }

    next();
  } catch (error) {
    console.error('Dashboard access middleware error:', error);
    return res.status(500).json({ message: 'Server error during dashboard access verification' });
  }
};

export { ROLE_PERMISSIONS };
