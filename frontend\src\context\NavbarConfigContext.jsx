import React, { createContext, useState, useContext, useEffect } from 'react';
import axios from 'axios';

const NavbarConfigContext = createContext();

export const useNavbarConfig = () => {
  const context = useContext(NavbarConfigContext);
  if (!context) {
    throw new Error('useNavbarConfig must be used within a NavbarConfigProvider');
  }
  return context;
};

export const NavbarConfigProvider = ({ children }) => {
  const [navbarConfig, setNavbarConfig] = useState({
    brandInfo: {
      companyName: 'Shopify',
      logo: ''
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch navbar configuration
  const fetchNavbarConfig = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/navbar/config');
      setNavbarConfig(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching navbar config:', err);
      setError(err.message);
      // Use default config if fetch fails
      setNavbarConfig({
        brandInfo: {
          companyName: 'Shopify',
          logo: ''
        }
      });
    } finally {
      setLoading(false);
    }
  };

  // Update navbar configuration
  const updateNavbarConfig = async (newConfig) => {
    try {
      const response = await axios.put('/api/navbar/config', newConfig);
      setNavbarConfig(response.data);
      return { success: true, data: response.data };
    } catch (err) {
      console.error('Error updating navbar config:', err);
      return { success: false, error: err.response?.data?.message || err.message };
    }
  };

  useEffect(() => {
    fetchNavbarConfig();
  }, []);

  const value = {
    navbarConfig,
    loading,
    error,
    fetchNavbarConfig,
    updateNavbarConfig
  };

  return (
    <NavbarConfigContext.Provider value={value}>
      {children}
    </NavbarConfigContext.Provider>
  );
};
