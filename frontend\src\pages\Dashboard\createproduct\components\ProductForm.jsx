import React from 'react';
import { Plus, FileText, Tag, LinkIcon } from 'lucide-react';
import ImageUploadSlot from './ImageUploadSlot';

const ProductForm = ({ 
    formData, 
    onFormChange, 
    imagePreviews, 
    onImageSelect, 
    categories, 
    navLinks, 
    onSubmit, 
    formLoading, 
    editingProduct 
}) => {
    // Filter categories based on selected navLink
    const getFilteredCategories = () => {
        if (!formData.navLink) {
            return categories;
        }
        return categories.filter(cat => !cat.navLink || cat.navLink._id === formData.navLink);
    };

    return (
        <div className="bg-white rounded-xl shadow-lg p-6 sm:p-8 mb-8">
            <form onSubmit={onSubmit} className="space-y-8">
                <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4 border-b pb-2">Product Images</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {Array(4).fill(null).map((_, index) => (
                            <ImageUploadSlot
                                key={index}
                                preview={imagePreviews[index]}
                                onSelect={onImageSelect}
                                index={index}
                            />
                        ))}
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label htmlFor="name" className="flex items-center text-sm font-semibold text-blue-800 mb-2">
                            <Plus size={16} className="mr-2" /> Product Name
                        </label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={onFormChange}
                            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>

                    <div>
                        <label htmlFor="price" className="flex items-center text-sm font-semibold text-blue-800 mb-2">
                            <Plus size={16} className="mr-2" /> Price
                        </label>
                        <input
                            type="number"
                            id="price"
                            name="price"
                            value={formData.price}
                            onChange={onFormChange}
                            step="0.01"
                            min="0"
                            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>

                    <div>
                        <label htmlFor="category" className="flex items-center text-sm font-semibold text-blue-800 mb-2">
                            <Tag size={16} className="mr-2" /> Category
                        </label>
                        <select 
                            id="category" 
                            name="category" 
                            value={formData.category} 
                            onChange={onFormChange}
                            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500"
                            required
                        >
                            <option value="">-- Select a Category --</option>
                            {getFilteredCategories().map(cat => (
                                <option key={cat._id} value={cat._id}>
                                    {cat.name} {cat.navLink ? `(${cat.navLink.title})` : '(General)'}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div>
                        <label htmlFor="navLink" className="flex items-center text-sm font-semibold text-blue-800 mb-2">
                            <LinkIcon size={16} className="mr-2" /> Assign to Page (Optional)
                        </label>
                        <select 
                            id="navLink" 
                            name="navLink" 
                            value={formData.navLink} 
                            onChange={onFormChange}
                            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">-- No Page --</option>
                            {navLinks.map(link => (
                                <option key={link._id} value={link._id}>
                                    {link.title} ({link.path})
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="md:col-span-2">
                        <label htmlFor="description" className="flex items-center text-sm font-semibold text-blue-800 mb-2">
                            <FileText size={16} className="mr-2" /> Description
                        </label>
                        <textarea
                            id="description"
                            name="description"
                            value={formData.description}
                            onChange={onFormChange}
                            rows="4"
                            className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div className="md:col-span-2">
                        <label className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                name="isNewProduct"
                                checked={formData.isNewProduct}
                                onChange={onFormChange}
                                className="rounded text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-700">Mark as New Arrival</span>
                        </label>
                    </div>
                </div>

                <div className="flex justify-end pt-4">
                    <button 
                        type="submit" 
                        disabled={formLoading}
                        className="px-8 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 transition disabled:bg-indigo-300 disabled:cursor-not-allowed"
                    >
                        {formLoading ? "Saving..." : editingProduct ? "Update Product" : "Create Product"}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default ProductForm;
