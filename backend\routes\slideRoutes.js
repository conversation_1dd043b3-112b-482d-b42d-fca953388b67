import express from 'express';
import {
  getActiveSlides,
  getAllSlidesForAdmin,
  createSlide,
  updateSlide,
  deleteSlide,
} from '../controllers/slideController.js';
import protect from '../middleware/authMiddleware.js';
import { requirePermission } from '../middleware/roleMiddleware.js';

const router = express.Router();

router.route('/').get(getActiveSlides).post(protect, requirePermission('slider'), createSlide);
router.route('/all').get(protect, requirePermission('slider'), getAllSlidesForAdmin);
router.route('/:id').patch(protect, requirePermission('slider'), updateSlide).delete(protect, requirePermission('slider'), deleteSlide);

export default router;