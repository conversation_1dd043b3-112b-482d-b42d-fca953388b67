import React from 'react';

const CallToAction = ({ ctaData }) => {
  // Use default values if ctaData is not provided
  const {
    title = "Ready to experience the future of e-commerce?",
    subtitle = "Join over 1 million customers who trust us for quality, innovation, and exceptional service.",
    primaryButton = { text: "Shop Our Collection", link: "/products" },
    secondaryButton = { text: "Contact Our Team", link: "/contact-us" },
    backgroundImage = ""
  } = ctaData || {};

  return (
    <div className="py-20">
      <div className="max-w-4xl mx-auto px-4 text-center">
        <div
          className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-3xl p-10 shadow-lg relative overflow-hidden"
          style={backgroundImage ? {
            backgroundImage: `url('${backgroundImage}')`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          } : {}}
        >
          {backgroundImage && (
            <div className="absolute inset-0 bg-gradient-to-r from-purple-900/80 to-indigo-900/80 rounded-3xl"></div>
          )}
          <div className="relative z-10">
            <h2 className={`text-3xl md:text-4xl font-bold mb-6 ${backgroundImage ? 'text-white' : 'text-gray-900'}`}>
              {title}
            </h2>
            <p className={`text-xl mb-8 max-w-2xl mx-auto ${backgroundImage ? 'text-gray-200' : 'text-gray-600'}`}>
              {subtitle}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a
                href={primaryButton.link}
                className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-4 rounded-full font-bold hover:from-purple-700 hover:to-indigo-700 transition-all shadow-lg inline-block"
              >
                {primaryButton.text}
              </a>
              <a
                href={secondaryButton.link}
                className={`px-8 py-4 rounded-full font-bold border-2 transition-all inline-block ${
                  backgroundImage
                    ? 'bg-white/10 text-white border-white hover:bg-white/20'
                    : 'bg-white text-gray-900 border-purple-600 hover:bg-purple-50'
                }`}
              >
                {secondaryButton.text}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallToAction;