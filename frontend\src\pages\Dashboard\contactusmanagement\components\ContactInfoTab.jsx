import React from 'react';
import { MapPin, Phone, Mail, Clock } from 'lucide-react';

const ContactInfoTab = ({ data, onChange }) => {
  const handleFieldChange = (field, value, lang = 'fr') => {
    onChange('contactInfo', {
      ...data.contactInfo,
      [field]: {
        ...data.contactInfo[field],
        [lang]: value
      }
    });
  };

  const handleSimpleFieldChange = (field, value) => {
    onChange('contactInfo', {
      ...data.contactInfo,
      [field]: value
    });
  };

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          📞 Contact Information Management
        </h2>
        <p className="text-gray-600">
          Manage contact details, address, phone numbers, and office hours
        </p>
      </div>

      {/* Company Address */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
          <MapPin size={20} className="mr-2 text-green-600" />
          Company Address
        </h3>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Street Address</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.contactInfo?.address?.fr || ''}
                  onChange={(e) => handleFieldChange('address', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="123 Rue de la Paix, Paris"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.contactInfo?.address?.ar || ''}
                  onChange={(e) => handleFieldChange('address', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-right"
                  placeholder="123 شارع السلام، الرياض"
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">City</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.contactInfo?.city?.fr || ''}
                  onChange={(e) => handleFieldChange('city', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Paris"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.contactInfo?.city?.ar || ''}
                  onChange={(e) => handleFieldChange('city', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-right"
                  placeholder="الرياض"
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Postal Code</label>
              <input
                type="text"
                value={data.contactInfo?.postalCode || ''}
                onChange={(e) => handleSimpleFieldChange('postalCode', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="75001"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">Country</label>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                  <input
                    type="text"
                    value={data.contactInfo?.country?.fr || ''}
                    onChange={(e) => handleFieldChange('country', e.target.value, 'fr')}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="France"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Details */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
          <Phone size={20} className="mr-2 text-green-600" />
          Contact Details
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Primary Phone</label>
            <input
              type="tel"
              value={data.contactInfo?.phone || ''}
              onChange={(e) => handleSimpleFieldChange('phone', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="+33 1 23 45 67 89"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Secondary Phone</label>
            <input
              type="tel"
              value={data.contactInfo?.secondaryPhone || ''}
              onChange={(e) => handleSimpleFieldChange('secondaryPhone', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="+33 1 23 45 67 90"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
            <input
              type="email"
              value={data.contactInfo?.email || ''}
              onChange={(e) => handleSimpleFieldChange('email', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Support Email</label>
            <input
              type="email"
              value={data.contactInfo?.supportEmail || ''}
              onChange={(e) => handleSimpleFieldChange('supportEmail', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>
        </div>
      </div>

      {/* Office Hours */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
          <Clock size={20} className="mr-2 text-green-600" />
          Office Hours
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Business Hours</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <textarea
                  value={data.contactInfo?.businessHours?.fr || ''}
                  onChange={(e) => handleFieldChange('businessHours', e.target.value, 'fr')}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Lundi - Vendredi: 9h00 - 18h00&#10;Samedi: 9h00 - 17h00&#10;Dimanche: Fermé"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <textarea
                  value={data.contactInfo?.businessHours?.ar || ''}
                  onChange={(e) => handleFieldChange('businessHours', e.target.value, 'ar')}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-right"
                  placeholder="الاثنين - الجمعة: 9:00 - 18:00&#10;السبت: 9:00 - 17:00&#10;الأحد: مغلق"
                  dir="rtl"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preview */}
      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Preview</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                <MapPin size={16} className="mr-2 text-green-600" />
                Address
              </h4>
              <p className="text-gray-600">
                {data.contactInfo?.address?.fr || 'No address set'}<br />
                {data.contactInfo?.city?.fr || 'No city set'} {data.contactInfo?.postalCode || ''}<br />
                {data.contactInfo?.country?.fr || 'No country set'}
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
                <Phone size={16} className="mr-2 text-green-600" />
                Contact
              </h4>
              <p className="text-gray-600">
                Phone: {data.contactInfo?.phone || 'No phone set'}<br />
                Email: {data.contactInfo?.email || 'No email set'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactInfoTab;
