import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import RemoveModal from '../../../hooks/RemoveModal';
import LinkForm from './components/LinkForm';
import RenderLinkTree from './components/LinkTree';

// Using relative URL since axios is configured globally

const NavbarManagementPage = () => {
  const [structuredLinks, setStructuredLinks] = useState([]);
  const [allLinks, setAllLinks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
  const [linkToDelete, setLinkToDelete] = useState(null);
  const initialState = { title: '', path: '', parentId: null, isDropdown: false };
  const [currentLink, setCurrentLink] = useState(initialState);
  
  const formRef = useRef(null);

  useEffect(() => {
    fetchNavLinks();
  }, []);

  const fetchNavLinks = async () => {
    try {
      setLoading(true);
      // Fetch the structured (nested) links for display
      const structuredRes = await axios.get('/api/navlinks');
      setStructuredLinks(structuredRes.data);

      // Fetch a flat list of all links for the parent dropdown
      const flatRes = await axios.get('/api/navlinks?flat=true');
      setAllLinks(flatRes.data);

      setError('');
    } catch (err) {
      setError('Failed to fetch navigation links.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setIsEditing(false);
    setCurrentLink(initialState);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      if (isEditing) {
        await axios.patch(`/api/navlinks/${currentLink._id}`, currentLink);
      } else {
        await axios.post('/api/navlinks', currentLink);
      }
      await fetchNavLinks();
      resetForm();
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to save link.');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (link) => {
    setIsEditing(true);
    setCurrentLink({ ...link, parentId: link.parentId || null });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleAddSubItem = (parentId) => {
    resetForm();
    setCurrentLink({ ...initialState, parentId });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleDelete = async (id) => {
    setLinkToDelete(id);
    setIsRemoveModalOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await axios.delete(`/api/navlinks/${linkToDelete}`);
      await fetchNavLinks();
      resetForm();
      setIsRemoveModalOpen(false);
      setLinkToDelete(null);
    } catch (err) {
      setError('Failed to delete link.');
    }
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">Navbar Management</h1>
      
      {error && (
        <div className="bg-red-100 text-red-700 p-3 rounded-lg mb-4">{error}</div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Navigation Structure</h2>
            {loading ? (
              <p>Loading...</p>
            ) : (
              <RenderLinkTree 
                links={structuredLinks} 
                onEdit={handleEdit}
                onDelete={handleDelete}
                onAddSubItem={handleAddSubItem}
              />
            )}
          </div>
        </div>
        <div ref={formRef}>
          <LinkForm 
            currentLink={currentLink}
            setCurrentLink={setCurrentLink}
            isEditing={isEditing}
            resetForm={resetForm}
            handleSubmit={handleSubmit}
            allLinks={allLinks}
          />
        </div>
      </div>

      <RemoveModal 
        isOpen={isRemoveModalOpen}
        onClose={() => {
          setIsRemoveModalOpen(false);
          setLinkToDelete(null);
        }}
        onConfirm={confirmDelete}
        itemName="navigation item"
      />
    </div>
  );
};

export default NavbarManagementPage;
