import express from 'express';
import ContactUs from '../models/ContactUs.js';
import protect from '../middleware/authMiddleware.js';
import { requirePermission } from '../middleware/roleMiddleware.js';

const router = express.Router();

// GET /api/contact-us - Get Contact Us content for frontend (public)
router.get('/', async (req, res) => {
  try {
    const { lang = 'fr' } = req.query;
    
    let contactUs = await ContactUs.findOne();
    
    if (!contactUs) {
      return res.status(404).json({ message: 'Contact Us content not found' });
    }

    // Transform data for frontend consumption based on language
    const transformedData = {
      contactInfo: {
        address: contactUs.contactInfo.address[lang] || contactUs.contactInfo.address.fr,
        city: contactUs.contactInfo.city[lang] || contactUs.contactInfo.city.fr,
        country: contactUs.contactInfo.country[lang] || contactUs.contactInfo.country.fr,
        postalCode: contactUs.contactInfo.postalCode,
        phone: contactUs.contactInfo.phone,
        secondaryPhone: contactUs.contactInfo.secondaryPhone,
        email: contactUs.contactInfo.email,
        supportEmail: contactUs.contactInfo.supportEmail,
        businessHours: contactUs.contactInfo.businessHours[lang] || contactUs.contactInfo.businessHours.fr
      },
      contactForm: {
        title: contactUs.contactForm.title[lang] || contactUs.contactForm.title.fr,
        subtitle: contactUs.contactForm.subtitle[lang] || contactUs.contactForm.subtitle.fr,
        labels: {
          name: contactUs.contactForm.labels.name[lang] || contactUs.contactForm.labels.name.fr,
          email: contactUs.contactForm.labels.email[lang] || contactUs.contactForm.labels.email.fr,
          subject: contactUs.contactForm.labels.subject[lang] || contactUs.contactForm.labels.subject.fr,
          message: contactUs.contactForm.labels.message[lang] || contactUs.contactForm.labels.message.fr
        },
        submitButton: contactUs.contactForm.submitButton[lang] || contactUs.contactForm.submitButton.fr,
        settings: {
          enableCaptcha: contactUs.contactForm.settings.enableCaptcha
        }
      },
      faq: {
        title: contactUs.faq.title[lang] || contactUs.faq.title.fr,
        subtitle: contactUs.faq.subtitle[lang] || contactUs.faq.subtitle.fr,
        questions: contactUs.faq.questions.map(q => ({
          question: q.question[lang] || q.question.fr,
          answer: q.answer[lang] || q.answer.fr,
          order: q.order
        }))
      }
    };

    res.json(transformedData);
  } catch (error) {
    console.error('Error fetching Contact Us content:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/contact-us/admin - Get raw Contact Us data for admin dashboard
router.get('/admin', protect, requirePermission('contact-us'), async (req, res) => {
  try {
    let contactUs = await ContactUs.findOne();
    
    if (!contactUs) {
      return res.status(404).json({ message: 'Contact Us content not found' });
    }

    res.json(contactUs);
  } catch (error) {
    console.error('Error fetching Contact Us admin data:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/contact-us - Create or update Contact Us content (admin only)
router.put('/', protect, requirePermission('contact-us'), async (req, res) => {
  try {
    const contactUsData = req.body;

    // Validate required structure
    if (!contactUsData.contactInfo || !contactUsData.contactForm) {
      return res.status(400).json({ message: 'Invalid Contact Us data structure' });
    }

    let contactUs = await ContactUs.findOne();

    if (contactUs) {
      // Update existing document
      Object.assign(contactUs, contactUsData);
      await contactUs.save();
    } else {
      // Create new document
      contactUs = new ContactUs(contactUsData);
      await contactUs.save();
    }

    res.json(contactUs);
  } catch (error) {
    console.error('Error saving Contact Us content:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/contact-us - Delete Contact Us content (admin only)
router.delete('/', protect, requirePermission('contact-us'), async (req, res) => {
  try {
    const contactUs = await ContactUs.findOneAndDelete();
    
    if (!contactUs) {
      return res.status(404).json({ message: 'Contact Us content not found' });
    }

    res.json({ message: 'Contact Us content deleted successfully' });
  } catch (error) {
    console.error('Error deleting Contact Us content:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

export default router;
