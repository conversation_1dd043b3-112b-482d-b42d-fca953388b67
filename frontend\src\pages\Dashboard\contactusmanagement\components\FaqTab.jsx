import React, { useState } from 'react';
import { Plus, Trash2, Edit, Save, X, HelpCircle } from 'lucide-react';

const FaqTab = ({ data, onChange }) => {
  const [editingIndex, setEditingIndex] = useState(null);
  const [editingData, setEditingData] = useState({});

  const handleSectionChange = (field, value, lang = 'fr') => {
    onChange('faq', {
      ...data.faq,
      [field]: {
        ...data.faq[field],
        [lang]: value
      }
    });
  };

  const addQuestion = () => {
    const currentQuestions = data.faq?.questions || [];
    const newQuestion = {
      question: { fr: '', ar: '' },
      answer: { fr: '', ar: '' },
      order: currentQuestions.length
    };
    
    onChange('faq', {
      ...data.faq,
      questions: [...currentQuestions, newQuestion]
    });
  };

  const removeQuestion = (index) => {
    const currentQuestions = data.faq?.questions || [];
    const newQuestions = currentQuestions.filter((_, i) => i !== index);
    onChange('faq', {
      ...data.faq,
      questions: newQuestions
    });
  };

  const startEditing = (index) => {
    setEditingIndex(index);
    setEditingData({ ...data.faq.questions[index] });
  };

  const saveEditing = () => {
    const currentQuestions = [...(data.faq?.questions || [])];
    currentQuestions[editingIndex] = editingData;
    onChange('faq', {
      ...data.faq,
      questions: currentQuestions
    });
    setEditingIndex(null);
    setEditingData({});
  };

  const cancelEditing = () => {
    setEditingIndex(null);
    setEditingData({});
  };

  const handleEditingChange = (field, value, lang = 'fr') => {
    if (field === 'order') {
      setEditingData({ ...editingData, [field]: value });
    } else {
      setEditingData({
        ...editingData,
        [field]: {
          ...editingData[field],
          [lang]: value
        }
      });
    }
  };

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-xl border border-yellow-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          ❓ FAQ Management
        </h2>
        <p className="text-gray-600">
          Manage frequently asked questions and answers with multilingual support
        </p>
      </div>

      {/* Section Title and Subtitle */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
          <HelpCircle size={20} className="mr-2 text-yellow-600" />
          FAQ Section Header
        </h3>
        
        <div className="space-y-6">
          {/* Section Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Section Title</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.faq?.title?.fr || ''}
                  onChange={(e) => handleSectionChange('title', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent focus:outline-none"
                  placeholder="Questions Fréquemment Posées"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.faq?.title?.ar || ''}
                  onChange={(e) => handleSectionChange('title', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent focus:outline-none text-right"
                  placeholder="الأسئلة الشائعة"
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Section Subtitle */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Section Subtitle</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <textarea
                  value={data.faq?.subtitle?.fr || ''}
                  onChange={(e) => handleSectionChange('subtitle', e.target.value, 'fr')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent focus:outline-none"
                  placeholder="Trouvez des réponses rapides aux questions courantes..."
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <textarea
                  value={data.faq?.subtitle?.ar || ''}
                  onChange={(e) => handleSectionChange('subtitle', e.target.value, 'ar')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent focus:outline-none text-right"
                  placeholder="اعثر على إجابات سريعة للأسئلة الشائعة..."
                  dir="rtl"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Questions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800">FAQ Questions</h3>
          <button
            onClick={addQuestion}
            className="flex items-center gap-2 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
          >
            <Plus size={16} />
            Add Question
          </button>
        </div>

        <div className="space-y-6">
          {(data.faq?.questions || []).map((question, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6">
              {editingIndex === index ? (
                /* Editing Mode */
                <div className="space-y-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-800">Edit FAQ Question</h4>
                    <div className="flex gap-2">
                      <button
                        onClick={saveEditing}
                        className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <Save size={16} />
                        Save
                      </button>
                      <button
                        onClick={cancelEditing}
                        className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <X size={16} />
                        Cancel
                      </button>
                    </div>
                  </div>

                  {/* Question */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">Question</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                        <input
                          type="text"
                          value={editingData.question?.fr || ''}
                          onChange={(e) => handleEditingChange('question', e.target.value, 'fr')}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent focus:outline-none"
                          placeholder="Question in French..."
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                        <input
                          type="text"
                          value={editingData.question?.ar || ''}
                          onChange={(e) => handleEditingChange('question', e.target.value, 'ar')}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent focus:outline-none text-right"
                          placeholder="السؤال بالعربية..."
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Answer */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">Answer</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                        <textarea
                          value={editingData.answer?.fr || ''}
                          onChange={(e) => handleEditingChange('answer', e.target.value, 'fr')}
                          rows={4}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent focus:outline-none"
                          placeholder="Answer in French..."
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                        <textarea
                          value={editingData.answer?.ar || ''}
                          onChange={(e) => handleEditingChange('answer', e.target.value, 'ar')}
                          rows={4}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent focus:outline-none text-right"
                          placeholder="الإجابة بالعربية..."
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                /* Display Mode */
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-800 mb-2">
                      {question.question?.fr || 'No question set'}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {question.answer?.fr || 'No answer set'}
                    </p>
                  </div>
                  
                  <div className="flex gap-2 ml-4">
                    <button
                      onClick={() => startEditing(index)}
                      className="p-2 text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => removeQuestion(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}

          {(!data.faq?.questions || data.faq.questions.length === 0) && (
            <div className="text-center py-12 text-gray-500">
              <p className="text-lg mb-2">No FAQ questions yet</p>
              <p className="text-sm">Click "Add Question" to get started</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FaqTab;
