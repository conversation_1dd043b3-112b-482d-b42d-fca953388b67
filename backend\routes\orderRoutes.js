import express from 'express';
import { createOrder, getAllOrders, confirmOrder } from '../controllers/orderController.js';
import protect from '../middleware/authMiddleware.js';
import { requirePermission } from '../middleware/roleMiddleware.js';

const router = express.Router();

// Route to create a new order and get all orders
router.route('/')
    .post(createOrder)
    .get(protect, requirePermission('orders'), getAllOrders);

// Route to confirm a specific order
router.route('/:id/confirm')
    .patch(protect, requirePermission('orders'), confirmOrder);

export default router;