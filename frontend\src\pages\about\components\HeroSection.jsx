import React from 'react';

const HeroSection = ({ scrollToTimeline, heroData }) => {
  // Use default values if heroData is not provided
  const {
    title = "Crafting the Future of",
    highlightedText = "E-commerce",
    subtitle = "We're redefining online shopping through innovation, quality, and a commitment to exceptional customer experiences.",
    buttonText = "Explore Our Journey",
    backgroundImage = "https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80"
  } = heroData || {};

  return (
    <div className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background with overlay */}
      <div className="absolute inset-0 bg-black">
        <div className="absolute inset-0 bg-gradient-to-b from-black/80 to-black/60 z-10"></div>
        <div className="absolute inset-0 bg-grid-white/[0.05] bg-[length:30px_30px] z-10"></div>
        <div
          className="absolute top-0 left-0 w-full h-full bg-cover bg-center opacity-70"
          style={{
            backgroundImage: `url('${backgroundImage}')`
          }}
        ></div>
      </div>

      {/* Hero Content */}
      <div className="relative z-20 text-center px-4 max-w-4xl">
        <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
          {title} <span className="bg-gradient-to-r from-purple-400 to-indigo-500 bg-clip-text text-transparent">{highlightedText}</span>
        </h1>

        <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">
          {subtitle}
        </p>

        <button
          onClick={scrollToTimeline}
          className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-4 rounded-full font-bold text-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center mx-auto"
        >
          {buttonText}
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 animate-bounce" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default HeroSection;