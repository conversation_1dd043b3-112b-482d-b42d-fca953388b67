import React, { useRef } from 'react';
import { Upload, X } from 'lucide-react';

const HeroSectionTab = ({ data, onChange }) => {
  const fileInputRef = useRef(null);

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        onChange('heroSection', {
          ...data.heroSection,
          backgroundImage: event.target.result
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    onChange('heroSection', {
      ...data.heroSection,
      backgroundImage: ''
    });
  };

  const handleFieldChange = (field, value, lang = 'fr') => {
    onChange('heroSection', {
      ...data.heroSection,
      [field]: {
        ...data.heroSection[field],
        [lang]: value
      }
    });
  };

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-xl border border-purple-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          🎯 Hero Section Management
        </h2>
        <p className="text-gray-600">
          Configure the main hero section that appears at the top of the About Us page
        </p>
      </div>

      {/* Background Image Upload */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Background Image</h3>
        
        <div className="space-y-4">
          {data.heroSection?.backgroundImage ? (
            <div className="relative">
              <img 
                src={data.heroSection.backgroundImage} 
                alt="Hero background" 
                className="w-full h-48 object-cover rounded-lg border-2 border-gray-200"
              />
              <button
                onClick={removeImage}
                className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
              >
                <X size={16} />
              </button>
            </div>
          ) : (
            <div 
              onClick={() => fileInputRef.current?.click()}
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-purple-400 hover:bg-purple-50 transition-colors"
            >
              <Upload size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 mb-2">Click to upload background image</p>
              <p className="text-sm text-gray-400">Recommended: 1920x1080px, JPG or PNG</p>
            </div>
          )}
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
          />
        </div>
      </div>

      {/* Hero Content */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">Hero Content</h3>

        <div className="space-y-8">
          {/* Main Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Main Title</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.heroSection?.title?.fr || ''}
                  onChange={(e) => handleFieldChange('title', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent focus:outline-none"
                  placeholder="Enter French title..."
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.heroSection?.title?.ar || ''}
                  onChange={(e) => handleFieldChange('title', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent focus:outline-none text-right"
                  placeholder="أدخل العنوان بالعربية..."
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Highlighted Text */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Highlighted Text (appears with gradient)</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.heroSection?.highlightedText?.fr || ''}
                  onChange={(e) => handleFieldChange('highlightedText', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent focus:outline-none"
                  placeholder="Highlighted text in French..."
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.heroSection?.highlightedText?.ar || ''}
                  onChange={(e) => handleFieldChange('highlightedText', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-right"
                  placeholder="النص المميز بالعربية..."
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Subtitle */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Subtitle</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <textarea
                  value={data.heroSection?.subtitle?.fr || ''}
                  onChange={(e) => handleFieldChange('subtitle', e.target.value, 'fr')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Enter French subtitle..."
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <textarea
                  value={data.heroSection?.subtitle?.ar || ''}
                  onChange={(e) => handleFieldChange('subtitle', e.target.value, 'ar')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-right"
                  placeholder="أدخل العنوان الفرعي بالعربية..."
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Button Text */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Button Text</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.heroSection?.buttonText?.fr || ''}
                  onChange={(e) => handleFieldChange('buttonText', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Button text in French..."
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.heroSection?.buttonText?.ar || ''}
                  onChange={(e) => handleFieldChange('buttonText', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-right"
                  placeholder="نص الزر بالعربية..."
                  dir="rtl"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Preview */}
      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Preview</h3>
        <div className="bg-black rounded-lg p-8 text-center relative overflow-hidden">
          {data.heroSection?.backgroundImage && (
            <div 
              className="absolute inset-0 bg-cover bg-center opacity-70"
              style={{ backgroundImage: `url(${data.heroSection.backgroundImage})` }}
            />
          )}
          <div className="relative z-10">
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-4">
              {data.heroSection?.title?.fr || 'Hero Title'}{' '}
              <span className="bg-gradient-to-r from-purple-400 to-indigo-500 bg-clip-text text-transparent">
                {data.heroSection?.highlightedText?.fr || 'Highlighted Text'}
              </span>
            </h1>
            <p className="text-gray-300 mb-6">
              {data.heroSection?.subtitle?.fr || 'Hero subtitle description...'}
            </p>
            <button className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-full font-bold">
              {data.heroSection?.buttonText?.fr || 'Button Text'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSectionTab;
