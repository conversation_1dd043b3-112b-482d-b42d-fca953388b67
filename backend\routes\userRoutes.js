import express from 'express';
import {
  getAllUsers,
  getUserById,
  updateUser<PERSON>ole,
  deleteUser,
  getUserProfile,
  updateUserProfile
} from '../controllers/userController.js';
import protect from '../middleware/authMiddleware.js';
import { requireAdmin } from '../middleware/roleMiddleware.js';

const router = express.Router();

// Public routes (none for users)

// Protected routes - require authentication
router.use(protect);

// User profile routes
router.route('/profile')
  .get(getUserProfile)
  .put(updateUserProfile);

// Admin only routes
router.route('/')
  .get(requireAdmin, getAllUsers);

router.route('/:id')
  .get(requireAdmin, getUserById)
  .delete(requireAdmin, deleteUser);

router.route('/:id/role')
  .put(requireAdmin, updateUserRole);

export default router;
