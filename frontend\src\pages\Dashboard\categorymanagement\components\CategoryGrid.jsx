import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import CategoryCard from './CategoryCard';

const CategoryGrid = ({ 
  loading, 
  filteredCategories, 
  searchQuery, 
  viewMode, 
  onDelete 
}) => {
  if (loading) {
    return (
      <div className={
        viewMode === 'grid'
          ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
          : "space-y-4"
      }>
        {[...Array(6)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 aspect-video rounded-t-xl" />
            <div className="bg-white p-4 rounded-b-xl">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
              <div className="h-4 bg-gray-200 rounded w-1/4" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <AnimatePresence>
      {filteredCategories.length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="text-center py-12"
        >
          <p className="text-gray-500 text-lg">
            {searchQuery ? 'No categories match your search' : 'No categories yet'}
          </p>
        </motion.div>
      ) : (
        <div className={
          viewMode === 'grid'
            ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
        }>
          {filteredCategories.map(category => (
            <CategoryCard 
              key={category._id} 
              category={category} 
              onDelete={onDelete}
            />
          ))}
        </div>
      )}
    </AnimatePresence>
  );
};

export default CategoryGrid;
