import Product from '../models/Product.js';
import NavLink from '../models/navLinkModel.js';

/**
 * Gets all products.
 * It populates both the 'navLink' and 'category' fields to include their details.
 */
export const getAllProducts = async (req, res) => {
  try {
    const { path, categoryId } = req.query;
    let query;

    // If a category is selected, get all products from that category regardless of path
    if (categoryId) {
      query = Product.find({ category: categoryId });
    }
    // If no category but path is provided (and not the main products page)
    else if (path && path !== '/products') {
      const navLink = await NavLink.findOne({ path: path });
      if (!navLink) {
        return res.json([]);
      }
      query = Product.find({ navLink: navLink._id });
    } else {
      // For the main '/products' page, or if no path is given
      query = Product.find({});
    }

    // Populate details for the found products
    const products = await query.populate('navLink').populate('category');
    
    res.json(products);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server Error: Could not fetch products." });
  }
};

/**
 * Gets a single product by its ID, also populating its category and navLink.
 */
export const getProductById = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate('category')
      .populate('navLink');
    
    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }
    res.json(product);
  } catch (error) {
    res.status(500).json({ message: "Server Error" });
  }
};

/**
 * Creates a new product with category and navLink assignments.
 */
export const createProduct = async (req, res) => {
  try {
    const { name, description, price, currency, pricing, originalPrice, isNewProduct, images, navLink, category } = req.body;
    
    if (!name || !price) {
      return res.status(400).json({ message: 'Name and price are required fields.' });
    }

    const product = new Product({
      name,
      description,
      currency: currency || 'USD',
      pricing: pricing || {
        withRevision: { price: parseFloat(price), originalPrice: originalPrice ? parseFloat(originalPrice) : null },
        withoutRevision: { price: parseFloat(price), originalPrice: originalPrice ? parseFloat(originalPrice) : null }
      },
      price: parseFloat(price), // Legacy field
      originalPrice: originalPrice ? parseFloat(originalPrice) : null,
      isNewProduct: isNewProduct || false,
      images: images || [],
      navLink: navLink || null,
      category: category || null
    });

    const savedProduct = await product.save();
    const populatedProduct = await Product.findById(savedProduct._id)
      .populate('category')
      .populate('navLink');

    res.status(201).json(populatedProduct);
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: 'Invalid product data.', error: error.message });
  }
};

/**
 * Updates a product's details, including its category and navLink assignments.
 */
export const updateProduct = async (req, res) => {
  try {
    const { name, description, price, isNewProduct, images, navLink, category } = req.body;
    
    const product = await Product.findById(req.params.id);

    if (product) {
      product.name = name || product.name;
      product.description = description || product.description;
      product.price = price ? parseFloat(price) : product.price;
      product.isNewProduct = isNewProduct !== undefined ? isNewProduct : product.isNewProduct;
      product.images = images || product.images;

      // Update navLink and category only if they're provided in the request
      if (navLink !== undefined) {
        product.navLink = navLink || null;
      }
      if (category !== undefined) {
        product.category = category || null;
      }

      const updatedProduct = await product.save();
      const populatedProduct = await Product.findById(updatedProduct._id)
        .populate('category')
        .populate('navLink');
      
      res.json(populatedProduct);
    } else {
      return res.status(404).json({ message: 'Product not found' });
    }
  } catch (error) {
    console.error(error);
    res.status(400).json({ message: 'Invalid product data.', error: error.message });
  }
};

/**
 * Deletes a product.
 */
export const deleteProduct = async (req, res) => {
  try {
    const product = await Product.findByIdAndDelete(req.params.id);
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }
    res.json({ message: 'Product removed successfully.' });
  } catch (error) {
    res.status(500).json({ message: 'Server Error' });
  }
};

/**
 * Gets related products based on the category of a given product.
 */
export const getRelatedProducts = async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }
    
    const relatedProducts = await Product.find({
      category: product.category,
      _id: { $ne: req.params.id }
    })
    .populate('category')
    .populate('navLink')
    .limit(4);

    res.json(relatedProducts);
  } catch (error) {
    console.error("Error fetching related products:", error);
    res.status(500).json({ message: "Server error" });
  }
};