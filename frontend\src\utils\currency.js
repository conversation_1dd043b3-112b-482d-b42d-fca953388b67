// Currency configuration and formatting utilities

export const CURRENCIES = {
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    position: 'before' // symbol position relative to amount
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    position: 'after'
  },
  DZD: {
    code: 'DZD',
    symbol: 'د.ج',
    name: 'Algerian Dinar',
    position: 'after'
  }
};

/**
 * Format price with currency symbol
 * @param {number} amount - The price amount
 * @param {string} currencyCode - Currency code (USD, EUR, DZD)
 * @param {object} options - Formatting options
 * @returns {string} Formatted price string
 */
export const formatPrice = (amount, currencyCode = 'USD', options = {}) => {
  const {
    showDecimals = true,
    locale = 'en-US'
  } = options;

  if (typeof amount !== 'number' || isNaN(amount)) {
    return '0';
  }

  const currency = CURRENCIES[currencyCode] || CURRENCIES.USD;
  const formattedAmount = showDecimals 
    ? amount.toFixed(2)
    : Math.round(amount).toString();

  // Format with locale-specific number formatting
  const numberFormatted = new Intl.NumberFormat(locale).format(
    showDecimals ? parseFloat(formattedAmount) : parseInt(formattedAmount)
  );

  if (currency.position === 'before') {
    return `${currency.symbol}${numberFormatted}`;
  } else {
    return `${numberFormatted} ${currency.symbol}`;
  }
};

/**
 * Get currency symbol
 * @param {string} currencyCode - Currency code
 * @returns {string} Currency symbol
 */
export const getCurrencySymbol = (currencyCode) => {
  return CURRENCIES[currencyCode]?.symbol || '$';
};

/**
 * Get currency name
 * @param {string} currencyCode - Currency code
 * @returns {string} Currency name
 */
export const getCurrencyName = (currencyCode) => {
  return CURRENCIES[currencyCode]?.name || 'US Dollar';
};

/**
 * Get all available currencies as array
 * @returns {Array} Array of currency objects
 */
export const getAllCurrencies = () => {
  return Object.values(CURRENCIES);
};

/**
 * Validate currency code
 * @param {string} currencyCode - Currency code to validate
 * @returns {boolean} True if valid currency code
 */
export const isValidCurrency = (currencyCode) => {
  return Object.keys(CURRENCIES).includes(currencyCode);
};

/**
 * Format price range (for products with different pricing options)
 * @param {number} minPrice - Minimum price
 * @param {number} maxPrice - Maximum price
 * @param {string} currencyCode - Currency code
 * @returns {string} Formatted price range
 */
export const formatPriceRange = (minPrice, maxPrice, currencyCode = 'USD') => {
  if (minPrice === maxPrice) {
    return formatPrice(minPrice, currencyCode);
  }
  return `${formatPrice(minPrice, currencyCode)} - ${formatPrice(maxPrice, currencyCode)}`;
};
