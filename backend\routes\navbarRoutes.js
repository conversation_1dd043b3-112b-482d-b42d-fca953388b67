import express from 'express';
import { getNavbarConfig, updateNavbarConfig } from '../controllers/navbarController.js';
import protect from '../middleware/authMiddleware.js';
import { requirePermission } from '../middleware/roleMiddleware.js';

const router = express.Router();

// Public route to get navbar configuration
router.get('/config', getNavbarConfig);

// Protected route to update navbar configuration
router.put('/config', protect, requirePermission('navbar'), updateNavbarConfig);

export default router;
