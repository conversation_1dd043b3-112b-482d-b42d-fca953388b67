import mongoose from 'mongoose';

const aboutUsSchema = new mongoose.Schema({
  heroSection: {
    title: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    highlightedText: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    subtitle: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    buttonText: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    backgroundImage: { type: String, default: '' }
  },
  coreValues: {
    vision: {
      title: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      description: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      points: {
        fr: [{ type: String }],
        ar: [{ type: String }]
      },
      image: { type: String, default: '' }
    },
    mission: {
      title: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      description: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      points: {
        fr: [{ type: String }],
        ar: [{ type: String }]
      },
      image: { type: String, default: '' }
    },
    story: {
      title: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      description: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      points: {
        fr: [{ type: String }],
        ar: [{ type: String }]
      },
      image: { type: String, default: '' }
    }
  },
  timeline: [{
    year: { type: String, required: true },
    title: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    description: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    milestones: {
      fr: [{ type: String }],
      ar: [{ type: String }]
    }
  }],
  ourMembers: {
    title: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    subtitle: {
      fr: { type: String, default: '' },
      ar: { type: String, default: '' }
    },
    members: [{
      name: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      position: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      description: {
        fr: { type: String, default: '' },
        ar: { type: String, default: '' }
      },
      image: { type: String, default: '' },
      order: { type: Number, default: 0 }
    }]
  }
}, {
  timestamps: true
});

export default mongoose.model('AboutUs', aboutUsSchema);
