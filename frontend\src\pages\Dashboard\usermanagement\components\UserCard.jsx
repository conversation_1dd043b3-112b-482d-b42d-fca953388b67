import { User, Crown, Shield, Eye, ShoppingCart, Settings } from 'lucide-react';

const UserCard = ({ user, currentUserId, onClick }) => {
  const isCurrentUser = user._id === currentUserId;

  const getRoleIcon = (role) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-5 w-5 text-yellow-600" />;
      case 'controller':
        return <Settings className="h-5 w-5 text-purple-600" />;
      case 'creator':
        return <Shield className="h-5 w-5 text-blue-600" />;
      case 'checker':
        return <Eye className="h-5 w-5 text-green-600" />;
      case 'buyer':
        return <ShoppingCart className="h-5 w-5 text-gray-600" />;
      default:
        return <User className="h-5 w-5 text-gray-400" />;
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'controller':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'creator':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'checker':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'buyer':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleDescription = (role) => {
    switch (role) {
      case 'admin':
        return 'Full system access';
      case 'controller':
        return 'Content management';
      case 'creator':
        return 'Product & category management';
      case 'checker':
        return 'Order management';
      case 'buyer':
        return 'Customer access only';
      default:
        return 'No specific permissions';
    }
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border transition-all duration-200 ${
        isCurrentUser 
          ? 'border-indigo-200 bg-indigo-50 cursor-not-allowed opacity-75' 
          : 'hover:shadow-md hover:border-indigo-200 cursor-pointer'
      }`}
      onClick={!isCurrentUser ? onClick : undefined}
    >
      <div className="p-6">
        {/* User Avatar and Basic Info */}
        <div className="flex items-start gap-4 mb-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-lg">
                {user.name.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {user.name}
              </h3>
              {isCurrentUser && (
                <span className="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">
                  You
                </span>
              )}
            </div>
            <p className="text-gray-600 text-sm truncate">{user.email}</p>
          </div>
        </div>

        {/* Role Badge */}
        <div className="flex items-center gap-2 mb-3">
          {getRoleIcon(user.role)}
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRoleColor(user.role)}`}>
            {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
          </span>
        </div>

        {/* Role Description */}
        <p className="text-gray-500 text-sm mb-4">
          {getRoleDescription(user.role)}
        </p>

        {/* User Details */}
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-500">Joined:</span>
            <span className="text-gray-900">
              {new Date(user.createdAt).toLocaleDateString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-500">Status:</span>
            <span className={`font-medium ${user.isVerified ? 'text-green-600' : 'text-red-600'}`}>
              {user.isVerified ? 'Verified' : 'Unverified'}
            </span>
          </div>
        </div>

        {/* Action Hint */}
        {!isCurrentUser && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <p className="text-xs text-gray-500 text-center">
              Click to manage role
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserCard;
