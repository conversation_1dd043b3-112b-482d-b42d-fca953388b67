import React, { useRef } from 'react';
import { Upload, X } from 'lucide-react';

const CallToActionTab = ({ data, onChange, language }) => {
  const fileInputRef = useRef(null);

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        onChange('callToAction', {
          ...data.callToAction,
          backgroundImage: event.target.result
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    onChange('callToAction', {
      ...data.callToAction,
      backgroundImage: ''
    });
  };

  const handleFieldChange = (field, value) => {
    onChange('callToAction', {
      ...data.callToAction,
      [field]: {
        ...data.callToAction[field],
        [language]: value
      }
    });
  };

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-xl border border-orange-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          🚀 Call to Action Management
        </h2>
        <p className="text-gray-600">
          Configure the call-to-action section at the bottom of the About Us page
        </p>
      </div>

      {/* Background Image Upload */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Background Image</h3>
        
        <div className="space-y-4">
          {data.callToAction?.backgroundImage ? (
            <div className="relative">
              <img 
                src={data.callToAction.backgroundImage} 
                alt="CTA background" 
                className="w-full h-48 object-cover rounded-lg border-2 border-gray-200"
              />
              <button
                onClick={removeImage}
                className="absolute top-2 right-2 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
              >
                <X size={16} />
              </button>
            </div>
          ) : (
            <div 
              onClick={() => fileInputRef.current?.click()}
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-orange-400 hover:bg-orange-50 transition-colors"
            >
              <Upload size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 mb-2">Click to upload background image</p>
              <p className="text-sm text-gray-400">Recommended: 1920x600px, JPG or PNG</p>
            </div>
          )}
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
          />
        </div>
      </div>

      {/* CTA Content */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">Call to Action Content ({language.toUpperCase()})</h3>
        
        <div className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title
            </label>
            <input
              type="text"
              value={data.callToAction?.title?.[language] || ''}
              onChange={(e) => handleFieldChange('title', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-lg"
              placeholder="Enter the CTA title..."
            />
          </div>

          {/* Subtitle */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subtitle
            </label>
            <textarea
              value={data.callToAction?.subtitle?.[language] || ''}
              onChange={(e) => handleFieldChange('subtitle', e.target.value)}
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="Enter the CTA subtitle description..."
            />
          </div>

          {/* Primary Button */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Primary Button Text
              </label>
              <input
                type="text"
                value={data.callToAction?.primaryButton?.text?.[language] || ''}
                onChange={(e) => onChange('callToAction', {
                  ...data.callToAction,
                  primaryButton: {
                    ...data.callToAction?.primaryButton,
                    text: {
                      ...data.callToAction?.primaryButton?.text,
                      [language]: e.target.value
                    }
                  }
                })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="Primary button text..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Primary Button Link
              </label>
              <input
                type="text"
                value={data.callToAction?.primaryButton?.link || ''}
                onChange={(e) => onChange('callToAction', {
                  ...data.callToAction,
                  primaryButton: {
                    ...data.callToAction?.primaryButton,
                    link: e.target.value
                  }
                })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="/contact-us"
              />
            </div>
          </div>

          {/* Secondary Button */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Secondary Button Text
              </label>
              <input
                type="text"
                value={data.callToAction?.secondaryButton?.text?.[language] || ''}
                onChange={(e) => onChange('callToAction', {
                  ...data.callToAction,
                  secondaryButton: {
                    ...data.callToAction?.secondaryButton,
                    text: {
                      ...data.callToAction?.secondaryButton?.text,
                      [language]: e.target.value
                    }
                  }
                })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="Secondary button text..."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Secondary Button Link
              </label>
              <input
                type="text"
                value={data.callToAction?.secondaryButton?.link || ''}
                onChange={(e) => onChange('callToAction', {
                  ...data.callToAction,
                  secondaryButton: {
                    ...data.callToAction?.secondaryButton,
                    link: e.target.value
                  }
                })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="/products"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Preview */}
      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Preview</h3>
        <div className="bg-gradient-to-r from-orange-600 to-red-600 rounded-lg p-8 text-center relative overflow-hidden">
          {data.callToAction?.backgroundImage && (
            <div 
              className="absolute inset-0 bg-cover bg-center opacity-30"
              style={{ backgroundImage: `url(${data.callToAction.backgroundImage})` }}
            />
          )}
          <div className="relative z-10">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
              {data.callToAction?.title?.[language] || 'CTA Title'}
            </h2>
            <p className="text-orange-100 mb-8 max-w-2xl mx-auto">
              {data.callToAction?.subtitle?.[language] || 'CTA subtitle description...'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-orange-600 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 transition-colors">
                {data.callToAction?.primaryButton?.text?.[language] || 'Primary Button'}
              </button>
              <button className="border-2 border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-white hover:text-orange-600 transition-colors">
                {data.callToAction?.secondaryButton?.text?.[language] || 'Secondary Button'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallToActionTab;
