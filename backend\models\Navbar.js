import mongoose from 'mongoose';

const navbarSchema = new mongoose.Schema({
  // Brand Information
  brandInfo: {
    companyName: {
      type: String,
      required: true,
      default: 'Shopify'
    },
    logo: {
      type: String, // Base64 image data or URL
      default: ''
    }
  },
  
  // Configuration settings
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

const Navbar = mongoose.model('Navbar', navbarSchema);

export default Navbar;
