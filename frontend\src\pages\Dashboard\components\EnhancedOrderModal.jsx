import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { X, CreditCard, Edit, CheckCircle } from 'lucide-react';
import { getAllWilayas, getMunicipalitiesForWilaya } from '../../../data/algerianWilayasMunicipalities';

const EnhancedOrderModal = ({ product, isOpen, onClose }) => {
  const [step, setStep] = useState(1); // 1: Payment Options, 2: Customer Info
  const [paymentOption, setPaymentOption] = useState('');
  
  const initialState = {
    firstName: '',
    lastName: '',
    wilaya: '',
    municipality: '',
    address: '',
    phone1: '',
    phone2: '',
  };

  const [formData, setFormData] = useState(initialState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [availableMunicipalities, setAvailableMunicipalities] = useState([]);

  // Get all wilayas for the dropdown
  const wilayas = getAllWilayas();

  // Payment options
  const paymentOptions = [
    {
      id: 'with-revision',
      title: 'Payment with Revision',
      description: 'You can request changes and revisions before final payment',
      icon: <Edit className="w-6 h-6" />,
      color: 'bg-blue-50 border-blue-200 text-blue-800',
      buttonColor: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      id: 'without-revision',
      title: 'Payment without Revision',
      description: 'Direct payment with no revision options - final sale',
      icon: <CreditCard className="w-6 h-6" />,
      color: 'bg-green-50 border-green-200 text-green-800',
      buttonColor: 'bg-green-600 hover:bg-green-700'
    }
  ];

  useEffect(() => {
    if (formData.wilaya) {
      const municipalities = getMunicipalitiesForWilaya(formData.wilaya);
      setAvailableMunicipalities(municipalities);
      setFormData(prev => ({ ...prev, municipality: '' }));
    }
  }, [formData.wilaya]);

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handlePaymentOptionSelect = (optionId) => {
    setPaymentOption(optionId);
    setStep(2);
  };

  const handleBackToPaymentOptions = () => {
    setStep(1);
    setPaymentOption('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const orderData = {
        productId: product._id,
        productName: product.name,
        productImage: product.images?.[0] || '',
        totalPrice: product.price,
        paymentOption: paymentOption,
        customerInfo: formData,
      };

      await axios.post('/api/orders', orderData);
      setSuccess(true);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to place order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetAndClose = () => {
    setStep(1);
    setPaymentOption('');
    setFormData(initialState);
    setError('');
    setSuccess(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">
            {step === 1 ? 'Choose Payment Option' : 'Order Details'}
          </h2>
          <button onClick={resetAndClose} className="p-2 hover:bg-gray-100 rounded-full transition-colors">
            <X size={24} className="text-gray-500" />
          </button>
        </div>

        {/* Product Info */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-4">
            <img src={product.images?.[0]} alt={product.name} className="w-16 h-16 object-cover rounded-lg" />
            <div>
              <h3 className="font-semibold text-lg text-gray-900">{product.name}</h3>
              <p className="text-2xl font-bold text-indigo-600">${product.price.toFixed(2)}</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {success ? (
            <div className="text-center py-8">
              <CheckCircle size={64} className="mx-auto text-green-500 mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Order Placed Successfully!</h3>
              <p className="text-gray-600 mb-6">Thank you for your order. We'll contact you soon to confirm the details.</p>
              <button onClick={resetAndClose} className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                Close
              </button>
            </div>
          ) : step === 1 ? (
            /* Payment Options Step */
            <div className="space-y-4">
              <p className="text-gray-600 mb-6">Please select your preferred payment option:</p>
              {paymentOptions.map((option) => (
                <div
                  key={option.id}
                  onClick={() => handlePaymentOptionSelect(option.id)}
                  className={`p-6 border-2 rounded-xl cursor-pointer transition-all hover:shadow-md ${option.color}`}
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {option.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold mb-2">{option.title}</h3>
                      <p className="text-sm opacity-80">{option.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            /* Customer Information Step */
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm">
                  {error}
                </div>
              )}

              {/* Selected Payment Option Display */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Selected Payment Option:</p>
                    <p className="font-semibold text-gray-900">
                      {paymentOptions.find(opt => opt.id === paymentOption)?.title}
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={handleBackToPaymentOptions}
                    className="text-indigo-600 hover:text-indigo-700 text-sm font-medium"
                  >
                    Change
                  </button>
                </div>
              </div>

              {/* Customer Form Fields */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">First Name *</label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Last Name *</label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Wilaya *</label>
                  <select
                    name="wilaya"
                    value={formData.wilaya}
                    onChange={handleFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    required
                  >
                    <option value="">Select Wilaya</option>
                    {wilayas.map((wilaya) => (
                      <option key={wilaya.code} value={wilaya.code}>
                        {wilaya.code} - {wilaya.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Municipality *</label>
                  <select
                    name="municipality"
                    value={formData.municipality}
                    onChange={handleFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    required
                    disabled={!formData.wilaya}
                  >
                    <option value="">Select Municipality</option>
                    {availableMunicipalities.map((municipality, index) => (
                      <option key={`${municipality.name}-${index}`} value={municipality.name}>
                        {municipality.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Address *</label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleFormChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  required
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Primary Phone *</label>
                  <input
                    type="tel"
                    name="phone1"
                    value={formData.phone1}
                    onChange={handleFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Optional Phone</label>
                  <input
                    type="tel"
                    name="phone2"
                    value={formData.phone2}
                    onChange={handleFormChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={handleBackToPaymentOptions}
                  className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="flex-1 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50"
                >
                  {loading ? 'Processing...' : `Place Order - $${product.price.toFixed(2)}`}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedOrderModal;
