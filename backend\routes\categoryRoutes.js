import express from 'express';
import mongoose from 'mongoose';
import Category from '../models/Category.js';
import Product from '../models/Product.js';
import NavLink from '../models/navLinkModel.js';
import protect from '../middleware/authMiddleware.js';
import { requirePermission } from '../middleware/roleMiddleware.js';

const router = express.Router();

/**
 * @desc    Fetch categories with product counts.
 * @route   GET /api/categories
 * @access  Public
 */
router.get('/', async (req, res) => {
    try {
        const { path } = req.query;
        const matchStage = {};

        if (path && path !== '/products') {
            const navLink = await NavLink.findOne({ path });
            if (navLink) {
                matchStage.navLink = navLink._id;
            } else {
                // If navLink is not found, return an empty array
                return res.json([]);
            }
        }
        
        const categories = await Category.aggregate([
            { $match: matchStage },
            {
                $lookup: {
                    from: Product.collection.name,
                    localField: '_id',
                    foreignField: 'category',
                    as: 'products'
                }
            },
            {
                $addFields: {
                    productCount: { $size: '$products' }
                }
            },
            {
                $project: {
                    products: 0 // Exclude the products array from the final output
                }
            },
            { $sort: { name: 1 } }
        ]);

        // Manually populate navLink details
        await Category.populate(categories, { 
            path: 'navLink', 
            select: 'title path' 
        });

        res.json(categories);
    } catch (error) {
        console.error('Error fetching categories:', error);
        res.status(500).json({ message: 'Server Error' });
    }
});

/**
 * @desc    Create a new category, optionally assigning it to a page.
 * @route   POST /api/categories
 * @access  Private/Admin
 */
router.post('/', protect, requirePermission('categories'), async (req, res) => {
    const { name, navLinkId, imageUrl, displayInCategoryPage, description, featuredOrder } = req.body;

    try {
        const categoryExists = await Category.findOne({ name, navLink: navLinkId || null });
        if (categoryExists) {
            return res.status(400).json({ message: 'This category already exists for the selected page.' });
        }

        const category = new Category({
            name,
            navLink: navLinkId || null,
            imageUrl: imageUrl || '',
            displayInCategoryPage: displayInCategoryPage || false,
            description: description || '',
            featuredOrder: featuredOrder || 0
        });

        const createdCategory = await category.save();
        res.status(201).json(createdCategory);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
});

/**
 * @desc    Delete a category
 * @route   DELETE /api/categories/:id
 * @access  Private/Admin
 */
router.delete('/:id', protect, requirePermission('categories'), async (req, res) => {
    try {
        const productCount = await Product.countDocuments({ category: new mongoose.Types.ObjectId(req.params.id) });

        if (productCount > 0) {
            return res.status(400).json({ message: `Cannot delete. Category is used by ${productCount} product(s).` });
        }

        const category = await Category.findByIdAndDelete(req.params.id);

        if (category) {
            res.json({ message: 'Category removed' });
        } else {
            res.status(404).json({ message: 'Category not found' });
        }
    } catch (error) {
        console.error('Error deleting category:', error);
        res.status(500).json({ message: 'Server Error' });
    }
});

export default router;