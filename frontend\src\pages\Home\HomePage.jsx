import React, { useState, useEffect } from "react";
import axios from "axios";
import { <PERSON> } from "react-router-dom";
import { Sparkles } from "lucide-react";
import HeroSlider from "./components/slider/Slide";
import ProductCard from "../Product/components/product_Card/ProductCard";
import CustomSlider from "./components/CustomSlider/CustomSlider"; // Import the custom slider
import PricingSelector from "../../components/PricingSelector";

const HomePage = () => {
  const [newProducts, setNewProducts] = useState([]);
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [slides, setSlides] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [slidesRes, productsRes] = await Promise.all([
          axios.get("http://localhost:5000/api/slides"),
          axios.get("http://localhost:5000/api/products"),
        ]);

        setSlides(slidesRes.data);
        const allProducts = productsRes.data;
        const filteredProducts = allProducts.filter((p) => p.isNewProduct);
        setNewProducts(filteredProducts);
        const shuffledProducts = [...allProducts].sort(
          () => 0.5 - Math.random()
        );
        setFeaturedProducts(shuffledProducts.slice(0, 8));
        setError("");
      } catch (error) {
        console.error("Error fetching homepage data:", error);
        setError("Could not load page content. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  return (
    <div>
      {slides.length > 0 && <HeroSlider slides={slides} />}

      <div className="container mx-auto px-4 py-16">
        <div className="flex flex-col items-center mb-8">
          <div className="flex items-center gap-2">
            <Sparkles className="text-amber-500" size={32} />
            <h2 className="text-3xl font-bold text-gray-800">New Arrivals</h2>
          </div>
          <div className="mt-2 w-24 h-1 bg-indigo-600 rounded-full"></div>

          {/* Pricing Selector */}
          <div className="mt-6">
            <PricingSelector />
          </div>
        </div>
        {loading ? (
          <p>Loading...</p>
        ) : error ? (
          <p>{error}</p>
        ) : newProducts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {newProducts.map((product) => (
              <ProductCard key={product._id} product={product} />
            ))}
          </div>
        ) : (
          <p>No new products found.</p>
        )}
      </div>

      {!loading && featuredProducts.length > 0 && (
        <div className="bg-gray-50">
          <div className="container mx-auto px-4 py-20">
            <div className="flex flex-col items-center mb-12">
              <h2 className="text-4xl font-bold text-gray-800 mb-2">
                You Might Also Like
              </h2>
              <p className="text-lg text-gray-600">
                Discover products picked just for you
              </p>
            </div>
            <div className="relative px-2 md:px-0">
              <CustomSlider products={featuredProducts} />
            </div>
          </div>
        </div>
      )}

      <div className="text-center py-12">
        <Link
          to="/products"
          className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold py-3 px-10 rounded-full text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
        >
          View All Products
        </Link>
      </div>
    </div>
  );
};

export default HomePage;
