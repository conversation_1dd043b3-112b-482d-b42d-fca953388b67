import React from 'react';
import LinkItem from './LinkItem';

const RenderLinkTree = ({ links, level = 0, ...props }) => (
  <div className="space-y-2" style={{ marginLeft: level > 0 ? '2rem' : '0' }}>
    {links.map(link => (
      <div key={link._id}>
        <LinkItem link={link} level={level} {...props} />
        {link.children && link.children.length > 0 && (
          <div className="mt-2">
            <RenderLinkTree links={link.children} level={level + 1} {...props} />
          </div>
        )}
      </div>
    ))}
  </div>
);

export default RenderLinkTree;
