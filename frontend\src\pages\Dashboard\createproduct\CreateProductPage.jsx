import React, { useState, useEffect } from 'react';
import axios from 'axios';
import RemoveModal from '../../../hooks/RemoveModal';
import ProductForm from './components/ProductForm';
import ProductTable from './components/ProductTable';
import { useAuth } from '../../../context/AuthContext';

const API_BASE_URL = 'http://localhost:5000/api';

const ProductManagementPage = () => {
    const [activeTab, setActiveTab] = useState('create');
    const [loading, setLoading] = useState(true);
    const [formLoading, setFormLoading] = useState(false);
    const [error, setError] = useState('');
    const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
    const [productToDelete, setProductToDelete] = useState(null);
    const { token } = useAuth();

    // Helper function to get auth headers
    const getAuthHeaders = () => ({
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });

    // State for data
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState([]);
    const [navLinks, setNavLinks] = useState([]);
    const [editingProduct, setEditingProduct] = useState(null);
    
    // State for the form
    const initialState = {
        name: '',
        description: '',
        price: '', // Legacy field for backward compatibility
        currency: 'USD',
        priceWithRevision: '',
        originalPriceWithRevision: '',
        priceWithoutRevision: '',
        originalPriceWithoutRevision: '',
        isNewProduct: false,
        navLink: '',
        category: ''
    };
    const [formData, setFormData] = useState(initialState);
    const [imageFiles, setImageFiles] = useState([null, null, null, null]); 
    const [imagePreviews, setImagePreviews] = useState([null, null, null, null]); 

    // Fetch initial data when component mounts
    useEffect(() => {
        const fetchInitialData = async () => {
            try {
                const [navLinkRes, categoryRes] = await Promise.all([
                    axios.get(`${API_BASE_URL}/navlinks?flat=true`),
                    axios.get(`${API_BASE_URL}/categories`)
                ]);
                setNavLinks(navLinkRes.data.filter(link => !link.isDropdown));
                setCategories(categoryRes.data);
            } catch (err) {
                console.error("Failed to fetch initial data", err);
                setError("Could not load page data. Please try refreshing.");
            }
        };
        fetchInitialData();
    }, []);

    // Fetch products list only when switching to the 'Manage' tab
    useEffect(() => {
        if (activeTab === 'manage') {
            fetchProducts();
        }
    }, [activeTab]);

    const fetchProducts = async () => {
        setLoading(true);
        setError('');
        try {
            const response = await axios.get(`${API_BASE_URL}/products`, getAuthHeaders());
            setProducts(response.data);
        } catch (err) {
            setError('Failed to fetch products.');
        } finally {
            setLoading(false);
        }
    };

    // Handle form field changes
    const handleFormChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    // Handle image selection
    const handleImageSelect = async (e, index) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const newPreviews = [...imagePreviews];
                newPreviews[index] = event.target.result;
                setImagePreviews(newPreviews);

                const newFiles = [...imageFiles];
                newFiles[index] = file;
                setImageFiles(newFiles);
            };
            reader.readAsDataURL(file);
        }
    };

    // Reset form to initial state
    const resetForm = () => {
        setFormData(initialState);
        setImageFiles([null, null, null, null]);
        setImagePreviews([null, null, null, null]);
        setEditingProduct(null);
        setError('');
    };

    // Handle form submission for both create and edit
    const handleSubmit = async (e) => {
        e.preventDefault();
        setFormLoading(true);
        setError('');

        try {
            // Validate required fields
            if (!formData.name || !formData.description) {
                setError('Please fill in all required fields.');
                setFormLoading(false);
                return;
            }

            // Validate pricing fields
            if (!formData.priceWithRevision || !formData.priceWithoutRevision) {
                setError('Please fill in both pricing options (with and without revision).');
                setFormLoading(false);
                return;
            }

            // Validate that prices are positive numbers
            if (parseFloat(formData.priceWithRevision) <= 0 || parseFloat(formData.priceWithoutRevision) <= 0) {
                setError('Prices must be positive numbers.');
                setFormLoading(false);
                return;
            }

            // Prepare images - combine existing images with new uploads
            const imagePromises = imageFiles.map((file, index) => {
                if (file) {
                    // If there's a new file, convert it to base64
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = () => resolve(reader.result);
                        reader.onerror = reject;
                        reader.readAsDataURL(file);
                    });
                } else if (imagePreviews[index]) {
                    // If there's no new file but there is a preview (existing image), keep it
                    return Promise.resolve(imagePreviews[index]);
                }
                return Promise.resolve(null);
            });

            const imageUrls = (await Promise.all(imagePromises)).filter(url => url !== null);

            // Prepare product data with new pricing structure
            const productData = {
                name: formData.name,
                description: formData.description,
                currency: formData.currency || 'USD',
                pricing: {
                    withRevision: {
                        price: parseFloat(formData.priceWithRevision),
                        originalPrice: formData.originalPriceWithRevision ? parseFloat(formData.originalPriceWithRevision) : null
                    },
                    withoutRevision: {
                        price: parseFloat(formData.priceWithoutRevision),
                        originalPrice: formData.originalPriceWithoutRevision ? parseFloat(formData.originalPriceWithoutRevision) : null
                    }
                },
                // Legacy price field for backward compatibility (use without revision price as default)
                price: parseFloat(formData.priceWithoutRevision),
                originalPrice: formData.originalPriceWithoutRevision ? parseFloat(formData.originalPriceWithoutRevision) : null,
                isNewProduct: formData.isNewProduct,
                navLink: formData.navLink || null,
                category: formData.category || null,
                images: imageUrls
            };

            let response;
            if (editingProduct) {
                // Update existing product
                response = await axios.patch(`${API_BASE_URL}/products/${editingProduct._id}`, productData, getAuthHeaders());
            } else {
                // Create new product
                response = await axios.post(`${API_BASE_URL}/products`, productData, getAuthHeaders());
            }

            // Reset form and fetch updated products list
            resetForm();
            await fetchProducts();
            setActiveTab('manage');

        } catch (err) {
            setError(err.response?.data?.message || 'Failed to save product.');
        } finally {
            setFormLoading(false);
        }
    };

    // Handle delete product
    const handleDelete = async (productId) => {
        setProductToDelete(productId);
        setIsRemoveModalOpen(true);
    };

    const confirmDelete = async () => {
        try {
            await axios.delete(`${API_BASE_URL}/products/${productToDelete}`, getAuthHeaders());
            await fetchProducts();
            setIsRemoveModalOpen(false);
            setProductToDelete(null);
        } catch (err) {
            setError('Failed to delete product.');
        }
    };

    // Handle edit product
    const handleEdit = (product) => {
        setEditingProduct(product);
        setFormData({
            name: product.name,
            description: product.description,
            price: product.price.toString(), // Legacy field
            currency: product.currency || 'USD',
            priceWithRevision: product.pricing?.withRevision?.price?.toString() || '',
            originalPriceWithRevision: product.pricing?.withRevision?.originalPrice?.toString() || '',
            priceWithoutRevision: product.pricing?.withoutRevision?.price?.toString() || product.price.toString(),
            originalPriceWithoutRevision: product.pricing?.withoutRevision?.originalPrice?.toString() || product.originalPrice?.toString() || '',
            isNewProduct: product.isNewProduct,
            navLink: product.navLink?._id || '',
            category: product.category?._id || ''
        });
        setImagePreviews(product.images);
        setImageFiles([null, null, null, null]); // Reset image files since we'll use existing URLs
        setActiveTab('create');
    };

    return (
        <div className="p-6">
            <div className="max-w-6xl mx-auto">
                <div className="mb-8">
                    <div className="flex items-center gap-4 border-b">
                        <button 
                            className={`px-5 py-3 font-medium ${activeTab === 'create' ? 'bg-indigo-600 text-white' : 'text-gray-600 hover:bg-gray-100'}`}
                            onClick={() => setActiveTab('create')}
                        >
                            {editingProduct ? 'Edit Product' : 'Create New'}
                        </button>
                        <button 
                            className={`px-5 py-3 font-medium ${activeTab === 'manage' ? 'bg-indigo-600 text-white' : 'text-gray-600 hover:bg-gray-100'}`}
                            onClick={() => {
                                setActiveTab('manage');
                                resetForm();
                            }}
                        >
                            Manage
                        </button>
                    </div>
                </div>
                
                {error && <div className="mb-6 p-4 bg-red-100 text-red-800 rounded-lg">{error}</div>}

                {activeTab === 'create' && (
                    <ProductForm
                        formData={formData}
                        onFormChange={handleFormChange}
                        imagePreviews={imagePreviews}
                        onImageSelect={handleImageSelect}
                        categories={categories}
                        navLinks={navLinks}
                        onSubmit={handleSubmit}
                        formLoading={formLoading}
                        editingProduct={editingProduct}
                    />
                )}

                {activeTab === 'manage' && (
                    <ProductTable
                        products={products}
                        loading={loading}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                    />
                )}
            </div>

            <RemoveModal 
                isOpen={isRemoveModalOpen}
                onClose={() => {
                    setIsRemoveModalOpen(false);
                    setProductToDelete(null);
                }}
                onConfirm={confirmDelete}
                itemName="product"
            />
        </div>
    );
};

export default ProductManagementPage;
