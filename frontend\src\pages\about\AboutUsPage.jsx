import React, { useRef, useState, useEffect } from 'react';
import HeroSection from './components/HeroSection';
import CoreValues from './components/CoreValues';
import Timeline from './components/Timeline';
import OurMembers from './components/OurMembers';

// Default fallback data in case API fails
const defaultData = {
  heroSection: {
    title: "Crafting the Future of",
    highlightedText: "E-commerce",
    subtitle: "We're redefining online shopping through innovation, quality, and a commitment to exceptional customer experiences.",
    buttonText: "Explore Our Journey",
    backgroundImage: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80"
  },
  coreValues: {
    vision: {
      title: "Our Vision",
      description: "To revolutionize the e-commerce experience by blending cutting-edge technology with human-centric design.",
      points: [
        "Creating seamless digital experiences that anticipate customer needs",
        "Building a platform where innovation meets accessibility",
        "Redefining online shopping through AI-powered personalization",
        "Establishing a global community of passionate creators and consumers"
      ],
      image: ""
    },
    mission: {
      title: "Our Mission",
      description: "To empower both customers and creators through a platform that values quality, sustainability, and innovation.",
      points: [
        "Curating exceptional products that tell a story",
        "Supporting independent creators and ethical manufacturing",
        "Implementing sustainable practices throughout our operations",
        "Delivering unparalleled customer experiences at every touchpoint"
      ],
      image: ""
    },
    story: {
      title: "Our Story",
      description: "Founded in 2018 by tech enthusiasts who saw the potential for a more meaningful e-commerce experience.",
      points: [
        "Started in a garage with 3 passionate founders",
        "Grew to 50+ employees within 2 years",
        "Served over 1 million satisfied customers worldwide",
        "Featured in Forbes 30 Under 30 for innovation in e-commerce"
      ],
      image: ""
    }
  },
  timeline: [
    {
      year: "2018",
      title: "The Beginning",
      description: "Founded in a small garage with big dreams to change e-commerce.",
      milestones: ["Launched with 3 founding members", "First 100 customers", "Featured in local tech magazine"]
    },
    {
      year: "2019",
      title: "First Milestones",
      description: "Established our brand identity and expanded product line.",
      milestones: ["Reached 10,000 customers", "Expanded to 5 product categories", "Opened first office space"]
    }
  ],
  ourMembers: {
    title: "Our Team",
    subtitle: "Meet the amazing people behind our success",
    members: []
  }
};

const AboutUsPage = () => {
  const timelineRef = useRef(null);
  const [aboutUsData, setAboutUsData] = useState(defaultData);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentLanguage, setCurrentLanguage] = useState('fr');

  const scrollToTimeline = () => {
    timelineRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Initialize language and fetch data
  useEffect(() => {
    const initLanguage = localStorage.getItem('selectedLanguage') || 'fr';
    setCurrentLanguage(initLanguage);

    const fetchAboutUsData = async () => {
      try {
        setLoading(true);
        setError('');

        const response = await fetch(`http://localhost:5000/api/about-us?lang=${initLanguage}`);

        if (!response.ok) {
          if (response.status === 404) {
            console.log('No About Us data found, using default data');
            setAboutUsData(defaultData);
          } else {
            throw new Error('Failed to fetch About Us data');
          }
        } else {
          const data = await response.json();
          setAboutUsData(data);
        }
      } catch (err) {
        console.error('Error fetching About Us data:', err);
        setError(err.message);
        setAboutUsData(defaultData);
      } finally {
        setLoading(false);
      }
    };

    fetchAboutUsData();
  }, []);

  // Listen for language changes and refetch data
  useEffect(() => {
    const handleLanguageChange = () => {
      const newLang = localStorage.getItem('selectedLanguage') || 'fr';
      setCurrentLanguage(newLang);

      // Refetch data for new language
      const fetchData = async () => {
        try {
          const response = await fetch(`http://localhost:5000/api/about-us?lang=${newLang}`);
          if (response.ok) {
            const data = await response.json();
            setAboutUsData(data);
          }
        } catch (err) {
          console.error('Error refetching data for language change:', err);
        }
      };
      fetchData();
    };

    // Listen for storage changes and custom language change events
    window.addEventListener('storage', handleLanguageChange);
    window.addEventListener('languageChange', handleLanguageChange);

    return () => {
      window.removeEventListener('storage', handleLanguageChange);
      window.removeEventListener('languageChange', handleLanguageChange);
    };
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading About Us content...</p>
        </div>
      </div>
    );
  }

  const isRTL = currentLanguage === 'ar';

  return (
    <div
      className={`min-h-screen bg-gradient-to-b from-gray-50 to-white ${isRTL ? 'rtl' : 'ltr'}`}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      {error && (
        <div className="bg-yellow-50 border border-yellow-200 p-4 text-center">
          <p className="text-yellow-800">Using default content due to: {error}</p>
        </div>
      )}
      <HeroSection
        scrollToTimeline={scrollToTimeline}
        heroData={aboutUsData.heroSection}
      />
      <CoreValues
        coreValuesData={aboutUsData.coreValues}
      />
      <Timeline
        timelineData={aboutUsData.timeline}
        ref={timelineRef}
      />
      <OurMembers
        membersData={aboutUsData.ourMembers}
      />
    </div>
  );
};

export default AboutUsPage;