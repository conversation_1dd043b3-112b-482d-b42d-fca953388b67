import React from 'react';
import { Plus, Trash2, Facebook, Twitter, Instagram, Linkedin, Youtube, ExternalLink } from 'lucide-react';

const SocialMediaTab = ({ 
  footerData, 
  addSocialMedia, 
  updateSocialMedia, 
  removeSocialMedia 
}) => {
  const getSocialIcon = (platform) => {
    const icons = {
      facebook: <Facebook size={16} />,
      twitter: <Twitter size={16} />,
      instagram: <Instagram size={16} />,
      linkedin: <Linkedin size={16} />,
      youtube: <Youtube size={16} />
    };
    return icons[platform] || <ExternalLink size={16} />;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Social Media Links</h2>
        <button
          onClick={addSocialMedia}
          className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          <Plus size={16} />
          Add Social Link
        </button>
      </div>

      <div className="space-y-4">
        {footerData?.socialMedia?.map((social, index) => (
          <div key={index} className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center gap-2">
              {getSocialIcon(social.platform)}
            </div>

            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
              <select
                value={social.platform}
                onChange={(e) => updateSocialMedia(index, 'platform', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="facebook">Facebook</option>
                <option value="twitter">Twitter</option>
                <option value="instagram">Instagram</option>
                <option value="linkedin">LinkedIn</option>
                <option value="youtube">YouTube</option>
                <option value="tiktok">TikTok</option>
                <option value="pinterest">Pinterest</option>
              </select>

              <input
                type="url"
                value={social.url}
                onChange={(e) => updateSocialMedia(index, 'url', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter social media URL"
              />
            </div>

            <button
              onClick={() => removeSocialMedia(index)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
            >
              <Trash2 size={16} />
            </button>
          </div>
        ))}

        {(!footerData?.socialMedia || footerData.socialMedia.length === 0) && (
          <div className="text-center py-8 text-gray-500">
            No social media links added yet. Click "Add Social Link" to get started.
          </div>
        )}
      </div>
    </div>
  );
};

export default SocialMediaTab;
