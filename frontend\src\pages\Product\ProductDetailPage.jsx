import React, { useState, useEffect, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { Star, ShoppingCart, CheckCircle, Heart, Share2, Shield, Truck, RotateCcw, Package } from 'lucide-react';
import { useCart } from '../../context/CartContext';
// Make sure this path points to your actual ProductCard component file
import ProductCard from './components/product_Card/ProductCard';
import EnhancedOrderModal from '../Dashboard/components/EnhancedOrderModal';
import { formatPrice } from '../../utils/currency';

// A skeleton component to show while the page data is loading.
const DetailPageSkeleton = () => (
    <div className="min-h-screen bg-gray-50">
      <div className="animate-pulse">
        <div className="w-full h-96 md:h-[500px] lg:h-[600px] bg-gray-200"></div>
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex gap-3 overflow-x-auto">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="w-20 h-20 bg-gray-200 rounded-lg flex-shrink-0"></div>
            ))}
          </div>
        </div>
        <div className="max-w-4xl mx-auto px-4 py-6 space-y-6">
          <div className="w-1/4 h-5 bg-gray-200 rounded-md"></div>
          <div className="w-3/4 h-8 bg-gray-300 rounded-md mb-4"></div>
          <div className="w-1/2 h-6 bg-gray-200 rounded-md"></div>
          <div className="space-y-3 pt-4">
            <div className="w-full h-4 bg-gray-200 rounded-md"></div>
            <div className="w-full h-4 bg-gray-200 rounded-md"></div>
            <div className="w-5/6 h-4 bg-gray-200 rounded-md"></div>
          </div>
          <div className="w-1/3 h-10 bg-gray-300 rounded-md pt-4"></div>
          <div className="w-full h-14 bg-gray-300 rounded-xl mt-6"></div>
        </div>
      </div>
    </div>
);

const ProductDetailPage = () => {
    const { id } = useParams();
    const { addToCart } = useCart();
    const [product, setProduct] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // State for the "Related Products" feature
    const [relatedProducts, setRelatedProducts] = useState([]);
    const [relatedLoading, setRelatedLoading] = useState(true);
  
    // State for the interactive gallery
    const [activeImage, setActiveImage] = useState('');
    const [isWishlisted, setIsWishlisted] = useState(false);

    // State for cart functionality
    const [quantity, setQuantity] = useState(1);
    const [isAdded, setIsAdded] = useState(false);

    // State for order modal
    const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);

    // Fetches both the main product and related products
    useEffect(() => {
        window.scrollTo(0, 0);
        
        const fetchProductAndRelated = async () => {
            setLoading(true);
            setRelatedLoading(true);
            setError(null);
        
            try {
                // Fetch the main product
                const productRes = await axios.get(`http://localhost:5000/api/products/${id}`);
                setProduct(productRes.data);
                if (productRes.data.images && productRes.data.images.length > 0) {
                    setActiveImage(productRes.data.images[0]);
                }
                setLoading(false); // Stop main page loading

                // Now fetch related products
                const relatedRes = await axios.get(`http://localhost:5000/api/products/related/${id}`);
                setRelatedProducts(relatedRes.data);

            } catch (err) {
                setError('Product not found. It may have been removed.');
                console.error('Failed to fetch product:', err);
                setLoading(false);
            } finally {
                setRelatedLoading(false);
            }
        };

        fetchProductAndRelated();
    }, [id]);

    const imageList = useMemo(() => product?.images || [], [product]);

    const handleQuantityChange = (amount) => {
        setQuantity(prev => Math.max(1, prev + amount));
    };

    const handleAddToCart = () => {
        if (product) {
            addToCart(product, quantity);
            setIsAdded(true);
            setTimeout(() => setIsAdded(false), 2500); // Reset button state after 2.5 seconds
        }
    };

    const toggleWishlist = () => {
        setIsWishlisted(!isWishlisted);
    };

    if (loading) return <DetailPageSkeleton />;

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
                <div className="text-center max-w-md">
                    <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span className="text-4xl" role="img" aria-label="Sad face">😞</span>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-4">{error}</h2>
                    <p className="text-gray-600 mb-8">The product you're looking for might have been moved or deleted.</p>
                    <Link to="/products" className="inline-flex items-center gap-2 bg-indigo-600 text-white py-3 px-6 rounded-xl hover:bg-indigo-700 transition-colors font-medium">
                        ← Back to All Products
                    </Link>
                </div>
            </div>
        );
    }

    if (!product) return null; // Render nothing if product is null after loading

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Image Section */}
            <div className="relative w-full h-96 md:h-[500px] lg:h-[600px] bg-white">
                <img src={activeImage || 'https://via.placeholder.com/800x600?text=No+Image'} alt={product.name} className="w-full h-full object-cover"/>
                <div className="absolute top-4 right-4 flex flex-col gap-3">
                    <button onClick={toggleWishlist} className={`w-12 h-12 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 ${isWishlisted ? 'bg-red-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}>
                        <Heart size={20} className={isWishlisted ? 'fill-current' : ''} />
                    </button>
                    <button className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-lg text-gray-700 hover:bg-gray-50 transition-colors"><Share2 size={20} /></button>
                </div>
                {product.stock > 0 ? (
                    <div className="absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">In Stock</div>
                ) : (
                    <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">Out of Stock</div>
                )}
            </div>

            {/* Thumbnail Gallery */}
            {imageList.length > 1 && (
                <div className="w-full bg-white border-b">
                    <div className="max-w-4xl mx-auto px-4 py-6">
                        <div className="flex gap-3 overflow-x-auto pb-2 scrollbar-hide">
                        {imageList.map((img, index) => (
                            <button key={index} onClick={() => setActiveImage(img)} className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-300 ${ activeImage === img ? 'border-indigo-500 ring-2 ring-indigo-200' : 'border-gray-200 hover:border-gray-300' }`}>
                                <img src={img} alt={`View ${index + 1}`} className="w-full h-full object-cover" />
                            </button>
                        ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Product Information */}
            <div className="bg-white">
                <div className="max-w-4xl mx-auto px-4 py-8">
                    <div className="mb-6">
                        {/* --- THIS IS THE FIX --- */}
                        {/* We access 'product.category.name' to render the name string. */}
                        {/* The '?.' (optional chaining) prevents errors if the category is missing. */}
                        {product.category?.name && (
                            <span className="inline-block bg-indigo-100 text-indigo-800 text-xs font-semibold px-3 py-1 rounded-full uppercase tracking-wide mb-3">
                                {product.category.name}
                            </span>
                        )}
                        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">{product.name}</h1>
                    </div>

                    {product.rating > 0 && (<div className="flex items-center gap-3 mb-6"><div className="flex items-center gap-1">{[...Array(5)].map((_, i) => (<Star key={i} size={20} className={`${i < Math.floor(product.rating) ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}/>)) }</div><span className="text-lg font-medium text-gray-700">{product.rating.toFixed(1)}</span><span className="text-gray-500">({product.reviewCount || 0} reviews)</span></div>)}
                    <div className="mb-8">
                        {/* Pricing Section */}
                        <div className="bg-gray-50 p-6 rounded-xl">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Pricing Options</h3>

                            {/* With Revision Option */}
                            {product.pricing?.withRevision && (
                                <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h4 className="font-semibold text-blue-800">With Revision Option</h4>
                                            <p className="text-sm text-blue-600">Includes revision requests</p>
                                        </div>
                                        <div className="text-right">
                                            <span className="text-2xl font-bold text-blue-800">
                                                {formatPrice(product.pricing.withRevision.price, product.currency)}
                                            </span>
                                            {product.pricing.withRevision.originalPrice && (
                                                <div className="text-sm text-blue-600 line-through">
                                                    {formatPrice(product.pricing.withRevision.originalPrice, product.currency)}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Without Revision Option */}
                            {product.pricing?.withoutRevision && (
                                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <h4 className="font-semibold text-green-800">Without Revision Option</h4>
                                            <p className="text-sm text-green-600">Final sale - no revisions</p>
                                        </div>
                                        <div className="text-right">
                                            <span className="text-2xl font-bold text-green-800">
                                                {formatPrice(product.pricing.withoutRevision.price, product.currency)}
                                            </span>
                                            {product.pricing.withoutRevision.originalPrice && (
                                                <div className="text-sm text-green-600 line-through">
                                                    {formatPrice(product.pricing.withoutRevision.originalPrice, product.currency)}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Legacy pricing display for backward compatibility */}
                            {!product.pricing && (
                                <div className="flex items-center gap-4 mb-2">
                                    <span className="text-4xl md:text-5xl font-bold text-gray-900">
                                        {formatPrice(product.price, product.currency)}
                                    </span>
                                    {product.originalPrice && (
                                        <span className="text-2xl text-gray-400 line-through">
                                            {formatPrice(product.originalPrice, product.currency)}
                                        </span>
                                    )}
                                </div>
                            )}

                            {product.originalPrice && !product.pricing && (
                                <div className="text-green-600 font-semibold">
                                    Save {formatPrice(product.originalPrice - product.price, product.currency)}
                                    ({(((product.originalPrice - product.price) / product.originalPrice) * 100).toFixed(0)}% off)
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="mb-8"><h3 className="text-xl font-semibold text-gray-900 mb-4">Description</h3><p className="text-gray-600 leading-relaxed text-lg">{product.description}</p></div>
                    <div className="mb-8"><div className="grid grid-cols-1 md:grid-cols-3 gap-4"><div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg"><Truck className="text-indigo-600" size={24} /><div><div className="font-semibold text-gray-900">Free Shipping</div><div className="text-sm text-gray-600">On orders over $50</div></div></div><div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg"><RotateCcw className="text-indigo-600" size={24} /><div><div className="font-semibold text-gray-900">30-Day Returns</div><div className="text-sm text-gray-600">Easy return policy</div></div></div><div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg"><Shield className="text-indigo-600" size={24} /><div><div className="font-semibold text-gray-900">Warranty</div><div className="text-sm text-gray-600">1-year protection</div></div></div></div></div>
                    
                    <div className="sticky bottom-0 bg-white border-t p-6 -mx-4 md:relative md:border-t-0 md:p-0 md:mx-0">
                        <div className="flex flex-col gap-4">
                            <div className="flex items-center gap-4 justify-center md:justify-start">
                                <label className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Quantity:</label>
                                <div className="flex items-center border-2 border-gray-300 rounded-full overflow-hidden">
                                    <button onClick={() => handleQuantityChange(-1)} disabled={quantity <= 1} className="w-12 h-12 flex items-center justify-center text-xl font-bold hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">−</button>
                                    <input type="text" value={quantity} readOnly className="w-16 text-center text-lg font-bold border-l border-r border-gray-300 h-12" />
                                    <button onClick={() => handleQuantityChange(1)} className="w-12 h-12 flex items-center justify-center text-xl font-bold hover:bg-gray-100 transition-colors">+</button>
                                </div>
                            </div>

                            <div className="flex flex-col sm:flex-row gap-3">
                                <button onClick={handleAddToCart} disabled={isAdded || product.stock === 0} className={`flex-1 h-14 px-8 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 ${isAdded ? 'bg-gradient-to-r from-emerald-500 to-green-500 text-white cursor-default' : product.stock === 0 ? 'bg-gray-400 text-white cursor-not-allowed' : 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white hover:shadow-lg hover:scale-105 active:scale-95'}`}>
                                    {isAdded ? (<><CheckCircle size={24} />Added to Cart!</>) : product.stock === 0 ? ('Out of Stock') : (<><ShoppingCart size={24} />Add to Cart</>)}
                                </button>

                                <button
                                    onClick={() => setIsOrderModalOpen(true)}
                                    disabled={product.stock === 0}
                                    className="flex-1 h-14 px-8 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white hover:shadow-lg hover:scale-105 active:scale-95 disabled:bg-gray-400 disabled:cursor-not-allowed"
                                >
                                    <Package size={24} />
                                    Order Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Related Products Section */}
            {!relatedLoading && relatedProducts.length > 0 && (
                <div className="py-16">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                            You Might Also Like
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                            {relatedProducts.map(relatedProd => (
                                <ProductCard key={relatedProd._id} product={relatedProd} />
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Enhanced Order Modal */}
            <EnhancedOrderModal
                product={product}
                isOpen={isOrderModalOpen}
                onClose={() => setIsOrderModalOpen(false)}
            />
        </div>
    );
};

export default ProductDetailPage;