import React, { createContext, useState, useContext, useEffect } from 'react';

const PricingContext = createContext();

export const usePricing = () => {
  const context = useContext(PricingContext);
  if (!context) {
    throw new Error('usePricing must be used within a PricingProvider');
  }
  return context;
};

export const PricingProvider = ({ children }) => {
  // Default to 'without-revision' for better pricing display
  const [selectedPricingOption, setSelectedPricingOption] = useState('without-revision');

  // Load pricing preference from localStorage on mount
  useEffect(() => {
    const savedPreference = localStorage.getItem('pricingPreference');
    if (savedPreference && ['with-revision', 'without-revision'].includes(savedPreference)) {
      setSelectedPricingOption(savedPreference);
    }
  }, []);

  // Save pricing preference to localStorage when it changes
  const updatePricingOption = (option) => {
    if (['with-revision', 'without-revision'].includes(option)) {
      setSelectedPricingOption(option);
      localStorage.setItem('pricingPreference', option);
    }
  };

  // Get the appropriate price for a product based on current selection
  const getProductPrice = (product) => {
    if (!product) return 0;

    // If product has new pricing structure
    if (product.pricing) {
      if (selectedPricingOption === 'with-revision' && product.pricing.withRevision) {
        return product.pricing.withRevision.price;
      } else if (selectedPricingOption === 'without-revision' && product.pricing.withoutRevision) {
        return product.pricing.withoutRevision.price;
      }
    }

    // Fallback to legacy price
    return product.price || 0;
  };

  // Get the appropriate original price for a product based on current selection
  const getProductOriginalPrice = (product) => {
    if (!product) return null;

    // If product has new pricing structure
    if (product.pricing) {
      if (selectedPricingOption === 'with-revision' && product.pricing.withRevision) {
        return product.pricing.withRevision.originalPrice;
      } else if (selectedPricingOption === 'without-revision' && product.pricing.withoutRevision) {
        return product.pricing.withoutRevision.originalPrice;
      }
    }

    // Fallback to legacy original price
    return product.originalPrice || null;
  };

  // Check if a product has different pricing options
  const hasMultiplePricingOptions = (product) => {
    if (!product || !product.pricing) return false;
    
    const withRevisionPrice = product.pricing.withRevision?.price;
    const withoutRevisionPrice = product.pricing.withoutRevision?.price;
    
    return withRevisionPrice && withoutRevisionPrice && withRevisionPrice !== withoutRevisionPrice;
  };

  // Get pricing options for a product
  const getPricingOptions = (product) => {
    if (!product) return [];

    const options = [];

    if (product.pricing?.withoutRevision?.price) {
      options.push({
        id: 'without-revision',
        label: 'Without Revision',
        description: 'Final sale - no revisions',
        price: product.pricing.withoutRevision.price,
        originalPrice: product.pricing.withoutRevision.originalPrice,
        recommended: true
      });
    }

    if (product.pricing?.withRevision?.price) {
      options.push({
        id: 'with-revision',
        label: 'With Revision',
        description: 'Includes revision requests',
        price: product.pricing.withRevision.price,
        originalPrice: product.pricing.withRevision.originalPrice,
        recommended: false
      });
    }

    // If no new pricing structure, create legacy option
    if (options.length === 0 && product.price) {
      options.push({
        id: 'legacy',
        label: 'Standard Price',
        description: 'Standard pricing',
        price: product.price,
        originalPrice: product.originalPrice,
        recommended: true
      });
    }

    return options;
  };

  const value = {
    selectedPricingOption,
    updatePricingOption,
    getProductPrice,
    getProductOriginalPrice,
    hasMultiplePricingOptions,
    getPricingOptions
  };

  return (
    <PricingContext.Provider value={value}>
      {children}
    </PricingContext.Provider>
  );
};
