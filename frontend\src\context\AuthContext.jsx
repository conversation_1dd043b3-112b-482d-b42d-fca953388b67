import React, { createContext, useState, useEffect, useContext } from 'react';

// 1. Create the context
export const AuthContext = createContext();

// Custom hook to use the AuthContext
export const useAuth = () => {
  return useContext(AuthContext);
};

// 2. Create the provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);

  // Effect to check for existing user info in localStorage on app start
  useEffect(() => {
    try {
      const storedUserInfo = localStorage.getItem('userInfo');
      if (storedUserInfo) {
        const userInfo = JSON.parse(storedUserInfo);
        setUser(userInfo);
        setToken(userInfo.token);
      }
    } catch (error) {
      console.error("Failed to parse user info from localStorage", error);
      setUser(null);
      setToken(null);
      localStorage.removeItem('userInfo');
    } finally {
      setLoading(false);
    }
  }, []);

  // --- NEW: More proactive user data refresh ---
  useEffect(() => {
    if (!token) return;

    // Function to handle refresh
    const handleRefresh = async () => {
      console.log('AuthContext: Proactively refreshing user data...');
      await refreshUser();
    };

    // Refresh user data every 2 minutes as a fallback
    const interval = setInterval(handleRefresh, 2 * 60 * 1000); // 2 minutes

    // --- NEW: Refresh user data when the window/tab gains focus ---
    // This is a great way to catch role changes made by an admin elsewhere.
    window.addEventListener('focus', handleRefresh);

    // Initial refresh when the effect is set up
    handleRefresh();

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleRefresh);
    };
  }, [token]);

  // Login function
  const login = (userData) => {
    console.log('AuthContext: User logged in with role:', userData.role);
    setUser(userData);
    setToken(userData.token);
    localStorage.setItem('userInfo', JSON.stringify(userData));
  };

  // Logout function
  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('userInfo');
    // Navigate to home to ensure a clean state after logout
    window.location.href = '/login'; 
  };

  // Refresh user data function
  const refreshUser = async () => {
    try {
      if (!token) return;

      // Import axios dynamically to prevent circular dependencies
      const axios = (await import('axios')).default;
      
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      const { data } = await axios.get('/api/users/profile', config);

      const updatedUserData = {
        ...user, // Keep existing data as a base
        ...data.data, // Overwrite with new data from server
        token: token, // Ensure the token is preserved
      };

      // Only log and update if the role has actually changed
      if (user?.role !== updatedUserData.role) {
        console.log('AuthContext: User role changed! Old:', user?.role, 'New:', updatedUserData.role);
      }
      
      setUser(updatedUserData);
      localStorage.setItem('userInfo', JSON.stringify(updatedUserData));

    } catch (error) {
      console.error('Failed to refresh user data:', error);
      if (error.response?.status === 401) {
        console.log('AuthContext: Auth error on refresh, logging out.');
        logout();
      }
    }
  };

  // Role-based utility functions
  const hasRole = (role) => {
    return user?.role === role;
  };

  const hasAnyRole = (roles) => {
    return roles.includes(user?.role);
  };

  const canAccessDashboard = () => {
    // Check if user exists and their role is not 'buyer'
    return !!user && user.role !== 'buyer';
  };

  const hasPermission = (permission) => {
    const rolePermissions = {
      buyer: [],
      checker: ['orders', 'overview'],
      creator: ['overview', 'products', 'categories', 'navbar'],
      controller: ['overview', 'footer', 'about-us', 'contact-us'],
      admin: ['overview', 'products', 'categories', 'navbar', 'orders', 'footer', 'about-us', 'contact-us', 'slider', 'users']
    };

    const userPermissions = rolePermissions[user?.role] || [];
    return userPermissions.includes(permission);
  };

  // The value provided to consuming components
  const value = {
    user,
    token,
    isLoggedIn: !!token,
    loading,
    login,
    logout,
    refreshUser,
    hasRole,
    hasAnyRole,
    canAccessDashboard,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};