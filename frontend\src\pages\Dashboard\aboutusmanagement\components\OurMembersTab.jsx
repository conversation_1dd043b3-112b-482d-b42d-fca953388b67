import React, { useState, useRef } from 'react';
import { Plus, Trash2, Edit, Save, X, Upload } from 'lucide-react';

const OurMembersTab = ({ data, onChange }) => {
  const [editingIndex, setEditingIndex] = useState(null);
  const [editingData, setEditingData] = useState({});
  const fileInputRefs = useRef({});

  const handleSectionChange = (field, value, lang = 'fr') => {
    onChange('ourMembers', {
      ...data.ourMembers,
      [field]: {
        ...data.ourMembers[field],
        [lang]: value
      }
    });
  };

  const addMember = () => {
    const currentMembers = data.ourMembers?.members || [];
    const newMember = {
      name: { fr: '', ar: '' },
      position: { fr: '', ar: '' },
      description: { fr: '', ar: '' },
      image: '',
      order: currentMembers.length
    };
    
    onChange('ourMembers', {
      ...data.ourMembers,
      members: [...currentMembers, newMember]
    });
  };

  const removeMember = (index) => {
    const currentMembers = data.ourMembers?.members || [];
    const newMembers = currentMembers.filter((_, i) => i !== index);
    onChange('ourMembers', {
      ...data.ourMembers,
      members: newMembers
    });
  };

  const startEditing = (index) => {
    setEditingIndex(index);
    setEditingData({ ...data.ourMembers.members[index] });
  };

  const saveEditing = () => {
    const currentMembers = [...(data.ourMembers?.members || [])];
    currentMembers[editingIndex] = editingData;
    onChange('ourMembers', {
      ...data.ourMembers,
      members: currentMembers
    });
    setEditingIndex(null);
    setEditingData({});
  };

  const cancelEditing = () => {
    setEditingIndex(null);
    setEditingData({});
  };

  const handleEditingChange = (field, value, lang = 'fr') => {
    if (field === 'order' || field === 'image') {
      setEditingData({ ...editingData, [field]: value });
    } else {
      setEditingData({
        ...editingData,
        [field]: {
          ...editingData[field],
          [lang]: value
        }
      });
    }
  };

  const handleImageUpload = (e, memberIndex = null) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (memberIndex !== null) {
          // Editing existing member
          setEditingData({
            ...editingData,
            image: event.target.result
          });
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = (memberIndex = null) => {
    if (memberIndex !== null) {
      setEditingData({
        ...editingData,
        image: ''
      });
    }
  };

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          👥 Our Members Management
        </h2>
        <p className="text-gray-600">
          Manage team member information, photos, and descriptions
        </p>
      </div>

      {/* Section Title and Subtitle */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">Section Header</h3>
        
        <div className="space-y-6">
          {/* Section Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Section Title</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.ourMembers?.title?.fr || ''}
                  onChange={(e) => handleSectionChange('title', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Our Team"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.ourMembers?.title?.ar || ''}
                  onChange={(e) => handleSectionChange('title', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  placeholder="فريقنا"
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Section Subtitle */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Section Subtitle</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <textarea
                  value={data.ourMembers?.subtitle?.fr || ''}
                  onChange={(e) => handleSectionChange('subtitle', e.target.value, 'fr')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Meet the amazing people behind our success..."
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <textarea
                  value={data.ourMembers?.subtitle?.ar || ''}
                  onChange={(e) => handleSectionChange('subtitle', e.target.value, 'ar')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  placeholder="تعرف على الأشخاص المذهلين وراء نجاحنا..."
                  dir="rtl"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Team Members */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800">Team Members</h3>
          <button
            onClick={addMember}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} />
            Add Member
          </button>
        </div>

        <div className="space-y-6">
          {(data.ourMembers?.members || []).map((member, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6">
              {editingIndex === index ? (
                /* Editing Mode */
                <div className="space-y-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-800">Edit Team Member</h4>
                    <div className="flex gap-2">
                      <button
                        onClick={saveEditing}
                        className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <Save size={16} />
                        Save
                      </button>
                      <button
                        onClick={cancelEditing}
                        className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <X size={16} />
                        Cancel
                      </button>
                    </div>
                  </div>

                  {/* Member Image */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">Member Photo</label>
                    {editingData.image ? (
                      <div className="relative w-32 h-32">
                        <img 
                          src={editingData.image} 
                          alt="Member" 
                          className="w-32 h-32 object-cover rounded-full border-4 border-gray-200"
                        />
                        <button
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    ) : (
                      <div 
                        onClick={() => fileInputRefs.current[`edit-${index}`]?.click()}
                        className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-full flex items-center justify-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors"
                      >
                        <div className="text-center">
                          <Upload size={24} className="mx-auto text-gray-400 mb-2" />
                          <p className="text-xs text-gray-500">Upload Photo</p>
                        </div>
                      </div>
                    )}
                    
                    <input
                      ref={(el) => fileInputRefs.current[`edit-${index}`] = el}
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleImageUpload(e, index)}
                      className="hidden"
                    />
                  </div>

                  {/* Member Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">Name</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                        <input
                          type="text"
                          value={editingData.name?.fr || ''}
                          onChange={(e) => handleEditingChange('name', e.target.value, 'fr')}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Member name in French..."
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                        <input
                          type="text"
                          value={editingData.name?.ar || ''}
                          onChange={(e) => handleEditingChange('name', e.target.value, 'ar')}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                          placeholder="اسم العضو بالعربية..."
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Member Position */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">Position</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                        <input
                          type="text"
                          value={editingData.position?.fr || ''}
                          onChange={(e) => handleEditingChange('position', e.target.value, 'fr')}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Position in French..."
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                        <input
                          type="text"
                          value={editingData.position?.ar || ''}
                          onChange={(e) => handleEditingChange('position', e.target.value, 'ar')}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                          placeholder="المنصب بالعربية..."
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Member Description */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-4">Description</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                        <textarea
                          value={editingData.description?.fr || ''}
                          onChange={(e) => handleEditingChange('description', e.target.value, 'fr')}
                          rows={3}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Member description in French..."
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                        <textarea
                          value={editingData.description?.ar || ''}
                          onChange={(e) => handleEditingChange('description', e.target.value, 'ar')}
                          rows={3}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                          placeholder="وصف العضو بالعربية..."
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                /* Display Mode */
                <div className="flex items-start gap-6">
                  <div className="flex-shrink-0">
                    {member.image ? (
                      <img 
                        src={member.image} 
                        alt={member.name?.fr || 'Team member'} 
                        className="w-20 h-20 object-cover rounded-full border-4 border-gray-200"
                      />
                    ) : (
                      <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-gray-400 text-2xl">👤</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-gray-800 mb-1">
                      {member.name?.fr || 'Unnamed Member'}
                    </h4>
                    <p className="text-blue-600 font-medium mb-2">
                      {member.position?.fr || 'No position'}
                    </p>
                    <p className="text-gray-600 text-sm">
                      {member.description?.fr || 'No description'}
                    </p>
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={() => startEditing(index)}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => removeMember(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}

          {(!data.ourMembers?.members || data.ourMembers.members.length === 0) && (
            <div className="text-center py-12 text-gray-500">
              <p className="text-lg mb-2">No team members yet</p>
              <p className="text-sm">Click "Add Member" to get started</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OurMembersTab;
