import User from '../models/User.js';
import jwt from 'jsonwebtoken';
import sendEmail from '../utils/sendEmail.js';
import crypto from 'crypto';

// --- Utility to generate JWT ---
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: '30d',
  });
};


// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
export const registerUser = async (req, res) => {
  const { name, email, password, role } = req.body;

  try {
    const userExists = await User.findOne({ email });

    if (userExists) {
      res.status(400);
      throw new Error('User already exists');
    }

    // Generate 6-digit verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Validate role if provided
    const validRoles = ['buyer', 'checker', 'creator', 'controller', 'admin'];
    const userRole = role && validRoles.includes(role) ? role : 'buyer';

    const user = await User.create({
      name,
      email,
      password,
      role: userRole,
      verificationCode,
      verificationCodeExpires: Date.now() + 10 * 60 * 1000, // 10 minutes
    });

    // Send verification email
    const message = `
      <h1>Account Verification</h1>
      <p>Thank you for registering. Please use the following code to verify your account:</p>
      <h2>${verificationCode}</h2>
      <p>This code will expire in 10 minutes.</p>
    `;

    await sendEmail({
      to: user.email,
      subject: 'Verify Your Email Address',
      text: message,
    });

    res.status(201).json({
      message: 'Registration successful. Please check your email for a verification code.',
      email: user.email, // Return email to help frontend direct the user
    });

  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};


// @desc    Verify user account
// @route   POST /api/auth/verify
// @access  Public
export const verifyUser = async (req, res) => {
  const { email, code } = req.body;

  try {
    const user = await User.findOne({
      email,
      verificationCode: code,
      verificationCodeExpires: { $gt: Date.now() },
    });

    if (!user) {
      res.status(400);
      throw new Error('Invalid or expired verification code.');
    }

    user.isVerified = true;
    user.verificationCode = undefined;
    user.verificationCodeExpires = undefined;
    await user.save();
    
    // User is verified, log them in immediately and send a token
    res.status(200).json({
      _id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      token: generateToken(user._id), // Send token upon successful verification
    });

  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};


// @desc    Auth user & get token (Login)
// @route   POST /api/auth/login
// @access  Public
export const loginUser = async (req, res) => {
  const { email, password } = req.body;

  try {
    const user = await User.findOne({ email });

    if (!user) {
      res.status(401);
      throw new Error('Invalid email or password');
    }

    if (!user.isVerified) {
        res.status(401);
        throw new Error('Account not verified. Please check your email.');
    }

    if (user && (await user.matchPassword(password))) {
      res.json({
        _id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        token: generateToken(user._id),
      });
    } else {
      res.status(401);
      throw new Error('Invalid email or password');
    }
  } catch (error) {
    res.status(res.statusCode || 400).json({ message: error.message });
  }
};