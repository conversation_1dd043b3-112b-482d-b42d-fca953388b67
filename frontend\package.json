{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.4", "framer-motion": "^12.23.3", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-i18next": "^15.6.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^5.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.2", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "swiper": "^11.2.10", "tailwindcss": "^4.1.4", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}