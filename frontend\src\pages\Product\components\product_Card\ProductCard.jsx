import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../../../../context/CartContext';
import { Heart, ShoppingCart, Eye, PackageCheck, CheckCircle, Zap } from 'lucide-react';
import OrderModal from '../../../Dashboard/components/OrderModal';
import { formatPrice, formatPriceRange } from '../../../../utils/currency';
import { usePricing } from '../../../../context/PricingContext';

/**
 * A responsive and interactive product card component.
 * It includes actions for viewing details, adding to cart, wishlisting, and placing a direct order.
 * @param {object} props - The component props.
 * @param {object} props.product - The product object to display.
 */
const ProductCard = ({ product }) => {
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const [isAdded, setIsAdded] = useState(false);
  const { getProductPrice, getProductOriginalPrice, hasMultiplePricingOptions } = usePricing();

  // State to control the visibility of the order modal
  const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);

  // Gracefully select the main image
  const mainImage = (product.images && product.images.length > 0)
    ? product.images[0]
    : product.imageData || 'https://via.placeholder.com/400?text=No+Image';

  // Handles adding the product to the cart
  const handleAddToCart = (e) => {
    e.stopPropagation(); 
    if (isAdded) return;
    addToCart(product, 1);
    setIsAdded(true);
    setTimeout(() => {
        setIsAdded(false);
    }, 2000);
  };

  // Toggles the wishlist state
  const handleWishlist = (e) => {
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
  };

  // Navigates to the detailed product page
  const navigateToDetail = () => {
    navigate(`/product/${product._id}`);
  };

  // Handler for the "View Details" button
  const handleQuickViewClick = (e) => {
    e.stopPropagation();
    navigateToDetail();
  };

  // Opens the order modal
  const handleOrderNowClick = (e) => {
    e.stopPropagation();
    setIsOrderModalOpen(true);
  };

  // Get pricing information using context
  const currency = product.currency || 'USD';
  const currentPrice = getProductPrice(product);
  const currentOriginalPrice = getProductOriginalPrice(product);
  const hasMultipleOptions = hasMultiplePricingOptions(product);

  // Calculate discount percentage
  const discountPercentage =
    currentOriginalPrice && currentOriginalPrice > currentPrice
      ? Math.round(((currentOriginalPrice - currentPrice) / currentOriginalPrice) * 100)
      : 0;
  
  // Determine stock status
  const stock = typeof product.stock === 'number' ? product.stock : 1;

  return (
    <>
      <div
        onClick={navigateToDetail}
        className="bg-white rounded-2xl shadow-sm overflow-hidden group transition-all duration-500 hover:shadow-2xl hover:-translate-y-2 cursor-pointer border border-transparent hover:border-indigo-300 relative"
        role="link"
        aria-label={`View details for ${product.name}`}
      >
        {/* --- BADGES & WISHLIST BUTTON --- */}
        <div className="absolute top-3 left-3 z-10 flex flex-col gap-2">
          {product.isNewProduct && <span className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1 shadow-lg"><Zap size={12} /> NEW</span>}
          {discountPercentage > 0 && <span className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">-{discountPercentage}%</span>}
          {stock < 5 && stock > 0 && <span className="bg-gradient-to-r from-orange-500 to-amber-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">Only {stock} left</span>}
        </div>

        <button onClick={handleWishlist} className="absolute top-3 right-3 z-10 w-9 h-9 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center transition-all duration-300 hover:bg-white hover:scale-110 shadow-lg" aria-label={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}>
          <Heart size={18} className={`transition-all duration-300 ${isWishlisted ? 'fill-red-500 text-red-500' : 'text-gray-600 hover:text-red-500'}`} />
        </button>

        {/* --- IMAGE CONTAINER WITH HOVER ACTIONS --- */}
        <div className="h-64 overflow-hidden relative bg-gray-50">
          {!imageLoaded && <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 animate-pulse" />}
          <img
            src={mainImage}
            alt={product.name}
            className={`w-full h-full object-cover group-hover:scale-110 transition-all duration-700 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            onLoad={() => setImageLoaded(true)}
            onError={() => setImageLoaded(true)}
          />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
            {/* Hover Action Buttons */}
            <div className="flex gap-3 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
              <button onClick={handleQuickViewClick} className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110" title="View Details">
                <Eye size={20} className="text-gray-700" />
              </button>
              <button onClick={handleAddToCart} disabled={stock === 0 || isAdded} className={`w-12 h-12 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 ${isAdded ? 'bg-emerald-500' : 'bg-gradient-to-r from-indigo-600 to-purple-600'} disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed`} title={stock === 0 ? 'Out of stock' : 'Add to Cart'}>
                {isAdded ? <CheckCircle size={20} className="text-white" /> : <ShoppingCart size={20} className="text-white" />}
              </button>
            </div>
          </div>
        </div>

        {/* --- CONTENT SECTION --- */}
        <div className="p-5">
          {/* --- THIS IS THE FIX --- */}
          {/* We now render the 'name' property of the category object. */}
          {product.category?.name && <span className="text-xs font-medium text-indigo-600 uppercase tracking-wide">{product.category.name}</span>}
          
          <h3 className="text-lg font-semibold text-gray-800 mt-2 mb-2 h-14 line-clamp-2 group-hover:text-indigo-600 transition-colors duration-300">{product.name}</h3>
          
          <div className="flex flex-col gap-1 mb-4">
            {/* Show pricing information */}
            <div className="flex items-baseline gap-2">
              <span className="text-2xl font-bold text-gray-900">{formatPrice(currentPrice, currency)}</span>
              {discountPercentage > 0 && (
                <span className="text-lg text-gray-500 line-through">{formatPrice(currentOriginalPrice, currency)}</span>
              )}
            </div>

            {/* Show multiple pricing indicator */}
            {hasMultipleOptions && (
              <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full w-fit">
                Multiple pricing options available
              </div>
            )}

            {discountPercentage > 0 && (
              <span className="text-xs font-semibold text-green-600 bg-green-100 px-2 py-1 rounded-full w-fit">
                {discountPercentage}% OFF
              </span>
            )}
          </div>
          
          {/* Action Buttons at the bottom of the card */}
          <div className="flex flex-col gap-2">
             <button onClick={handleAddToCart} disabled={stock === 0 || isAdded} className={`w-full py-3 px-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center gap-2 text-base ${isAdded ? 'bg-emerald-500 text-white cursor-default' : stock === 0 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-white text-indigo-600 border-2 border-indigo-600 hover:bg-indigo-50'}`}>
              {isAdded ? <><CheckCircle size={18} /> Added!</> : <><ShoppingCart size={18} />{stock === 0 ? 'Out of Stock' : 'Add to Cart'}</>}
            </button>
            <button onClick={handleOrderNowClick} disabled={stock === 0} className="w-full py-3 px-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center gap-2 text-base bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700 hover:shadow-lg active:scale-95 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed">
                <PackageCheck size={18} /> Order Now
            </button>
          </div>
        </div>
      </div>
      
      {/* The modal is only rendered in the DOM when isOrderModalOpen is true */}
      {isOrderModalOpen && (
        <OrderModal 
          product={product}
          isOpen={isOrderModalOpen}
          onClose={() => setIsOrderModalOpen(false)}
        />
      )}
    </>
  );
};

export default ProductCard;