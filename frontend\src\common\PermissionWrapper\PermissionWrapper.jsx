import { useAuth } from '../../context/AuthContext';
import AccessDenied from '../AccessDenied/AccessDenied';

const PermissionWrapper = ({ permission, children }) => {
  const { hasPermission } = useAuth();

  if (!hasPermission(permission)) {
    return (
      <AccessDenied 
        title="Access Restricted"
        message="Your role doesn't support access to this page"
        backTo="/dashboard"
      />
    );
  }

  return children;
};

export default PermissionWrapper;
