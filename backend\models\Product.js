import mongoose from 'mongoose';

const productSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },

  // Currency and pricing system
  currency: {
    type: String,
    enum: ['USD', 'EUR', 'DZD'],
    default: 'USD',
    required: true
  },

  // Pricing structure
  pricing: {
    withRevision: {
      price: { type: Number },
      originalPrice: { type: Number }
    },
    withoutRevision: {
      price: { type: Number },
      originalPrice: { type: Number }
    }
  },

  // Legacy fields for backward compatibility
  price: { type: Number, required: true },
  images: [{ type: String }],
  originalPrice: { type: Number },
  stock: { type: Number, default: 1 },
  isNewProduct: { type: Boolean, default: false },
  
  // --- MODIFIED LINE ---
  // The category field now stores the ID of a document in the 'Category' collection.
  category: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Category', // This 'ref' tells Mongoose which model to use during population.
    required: true 
  },
  // ---------------------

  rating: { type: Number },
  reviewCount: { type: Number },
  navLink: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'NavLink',
    required: false,
  },
}, { timestamps: true });

const Product = mongoose.model('Product', productSchema);

export default Product;