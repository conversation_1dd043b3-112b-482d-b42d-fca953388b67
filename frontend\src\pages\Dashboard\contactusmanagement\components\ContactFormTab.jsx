import React from 'react';
import { MessageSquare, Send } from 'lucide-react';

const ContactFormTab = ({ data, onChange }) => {
  const handleFieldChange = (field, value, lang = 'fr') => {
    onChange('contactForm', {
      ...data.contactForm,
      [field]: {
        ...data.contactForm[field],
        [lang]: value
      }
    });
  };

  const handleSettingChange = (field, value) => {
    onChange('contactForm', {
      ...data.contactForm,
      settings: {
        ...data.contactForm?.settings,
        [field]: value
      }
    });
  };

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
        <h2 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          📝 Contact Form Management
        </h2>
        <p className="text-gray-600">
          Configure contact form content, labels, and settings
        </p>
      </div>

      {/* Form Content */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
          <MessageSquare size={20} className="mr-2 text-blue-600" />
          Form Content
        </h3>
        
        <div className="space-y-6">
          {/* Form Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Form Title</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.contactForm?.title?.fr || ''}
                  onChange={(e) => handleFieldChange('title', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Contactez-nous"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.contactForm?.title?.ar || ''}
                  onChange={(e) => handleFieldChange('title', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  placeholder="اتصل بنا"
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Form Subtitle */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Form Subtitle</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <textarea
                  value={data.contactForm?.subtitle?.fr || ''}
                  onChange={(e) => handleFieldChange('subtitle', e.target.value, 'fr')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Nous aimerions avoir de vos nouvelles..."
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <textarea
                  value={data.contactForm?.subtitle?.ar || ''}
                  onChange={(e) => handleFieldChange('subtitle', e.target.value, 'ar')}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  placeholder="نود أن نسمع منك..."
                  dir="rtl"
                />
              </div>
            </div>
          </div>

          {/* Form Labels */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Name Field Label */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">Name Field Label</label>
              <div className="space-y-3">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">🇫🇷 French</label>
                  <input
                    type="text"
                    value={data.contactForm?.labels?.name?.fr || ''}
                    onChange={(e) => handleFieldChange('labels', { ...data.contactForm?.labels, name: { ...data.contactForm?.labels?.name, fr: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    placeholder="Nom complet"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">🇸🇦 Arabic</label>
                  <input
                    type="text"
                    value={data.contactForm?.labels?.name?.ar || ''}
                    onChange={(e) => handleFieldChange('labels', { ...data.contactForm?.labels, name: { ...data.contactForm?.labels?.name, ar: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm text-right"
                    placeholder="الاسم الكامل"
                    dir="rtl"
                  />
                </div>
              </div>
            </div>

            {/* Email Field Label */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">Email Field Label</label>
              <div className="space-y-3">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">🇫🇷 French</label>
                  <input
                    type="text"
                    value={data.contactForm?.labels?.email?.fr || ''}
                    onChange={(e) => handleFieldChange('labels', { ...data.contactForm?.labels, email: { ...data.contactForm?.labels?.email, fr: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    placeholder="Adresse e-mail"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">🇸🇦 Arabic</label>
                  <input
                    type="text"
                    value={data.contactForm?.labels?.email?.ar || ''}
                    onChange={(e) => handleFieldChange('labels', { ...data.contactForm?.labels, email: { ...data.contactForm?.labels?.email, ar: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm text-right"
                    placeholder="عنوان البريد الإلكتروني"
                    dir="rtl"
                  />
                </div>
              </div>
            </div>

            {/* Subject Field Label */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">Subject Field Label</label>
              <div className="space-y-3">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">🇫🇷 French</label>
                  <input
                    type="text"
                    value={data.contactForm?.labels?.subject?.fr || ''}
                    onChange={(e) => handleFieldChange('labels', { ...data.contactForm?.labels, subject: { ...data.contactForm?.labels?.subject, fr: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    placeholder="Sujet"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">🇸🇦 Arabic</label>
                  <input
                    type="text"
                    value={data.contactForm?.labels?.subject?.ar || ''}
                    onChange={(e) => handleFieldChange('labels', { ...data.contactForm?.labels, subject: { ...data.contactForm?.labels?.subject, ar: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm text-right"
                    placeholder="الموضوع"
                    dir="rtl"
                  />
                </div>
              </div>
            </div>

            {/* Message Field Label */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">Message Field Label</label>
              <div className="space-y-3">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">🇫🇷 French</label>
                  <input
                    type="text"
                    value={data.contactForm?.labels?.message?.fr || ''}
                    onChange={(e) => handleFieldChange('labels', { ...data.contactForm?.labels, message: { ...data.contactForm?.labels?.message, fr: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    placeholder="Message"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">🇸🇦 Arabic</label>
                  <input
                    type="text"
                    value={data.contactForm?.labels?.message?.ar || ''}
                    onChange={(e) => handleFieldChange('labels', { ...data.contactForm?.labels, message: { ...data.contactForm?.labels?.message, ar: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm text-right"
                    placeholder="الرسالة"
                    dir="rtl"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button Text */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">Submit Button Text</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇫🇷 French</label>
                <input
                  type="text"
                  value={data.contactForm?.submitButton?.fr || ''}
                  onChange={(e) => handleFieldChange('submitButton', e.target.value, 'fr')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Envoyer le message"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-2">🇸🇦 Arabic</label>
                <input
                  type="text"
                  value={data.contactForm?.submitButton?.ar || ''}
                  onChange={(e) => handleFieldChange('submitButton', e.target.value, 'ar')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  placeholder="إرسال الرسالة"
                  dir="rtl"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Form Settings */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
          <Send size={20} className="mr-2 text-blue-600" />
          Form Settings
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Email</label>
            <input
              type="email"
              value={data.contactForm?.settings?.recipientEmail || ''}
              onChange={(e) => handleSettingChange('recipientEmail', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Auto-reply Email</label>
            <input
              type="email"
              value={data.contactForm?.settings?.autoReplyEmail || ''}
              onChange={(e) => handleSettingChange('autoReplyEmail', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div className="md:col-span-2">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={data.contactForm?.settings?.enableCaptcha || false}
                onChange={(e) => handleSettingChange('enableCaptcha', e.target.checked)}
                className="rounded text-blue-600 focus:ring-blue-500 h-4 w-4"
              />
              <span className="text-sm text-gray-700">Enable CAPTCHA verification</span>
            </label>
          </div>
        </div>
      </div>

      {/* Preview */}
      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Preview (French)</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {data.contactForm?.title?.fr || 'Contact Form Title'}
          </h3>
          <p className="text-gray-600 mb-6">
            {data.contactForm?.subtitle?.fr || 'Contact form subtitle...'}
          </p>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {data.contactForm?.labels?.name?.fr || 'Name'}
                </label>
                <input type="text" className="w-full px-3 py-2 border border-gray-300 rounded-lg" disabled />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {data.contactForm?.labels?.email?.fr || 'Email'}
                </label>
                <input type="email" className="w-full px-3 py-2 border border-gray-300 rounded-lg" disabled />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {data.contactForm?.labels?.subject?.fr || 'Subject'}
              </label>
              <input type="text" className="w-full px-3 py-2 border border-gray-300 rounded-lg" disabled />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {data.contactForm?.labels?.message?.fr || 'Message'}
              </label>
              <textarea rows={4} className="w-full px-3 py-2 border border-gray-300 rounded-lg" disabled />
            </div>
            
            <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium" disabled>
              {data.contactForm?.submitButton?.fr || 'Submit'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactFormTab;
