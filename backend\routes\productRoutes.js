import express from 'express';
import {
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  // --- IMPORT THE NEW CONTROLLER ---
  getRelatedProducts,
} from '../controllers/productsController.js';
import protect from '../middleware/authMiddleware.js';
import { requirePermission } from '../middleware/roleMiddleware.js';

const router = express.Router();

/*
 * This file defines the API endpoints and maps them to the corresponding controller functions.
 * The base path for all routes in this file is '/api/products', which is configured in index.js.
 */

// --- START OF NEW ROUTE ---
// This route must be defined *before* the '/:id' route to avoid conflicts.
// It finds products related to the one specified by the ID.
router.get('/related/:id', getRelatedProducts);
// --- END OF NEW ROUTE ---


// Route for fetching all products and creating a new product
router.route('/')
  .get(getAllProducts)
  .post(protect, requirePermission('products'), createProduct);


// Route for operations on a single product, identified by its MongoDB _id
router.route('/:id')
  .get(getProductById)
  .patch(protect, requirePermission('products'), updateProduct)
  .delete(protect, requirePermission('products'), deleteProduct);


export default router;