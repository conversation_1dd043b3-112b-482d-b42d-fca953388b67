import express from 'express';
import NavLink from '../models/navLinkModel.js';
import protect from '../middleware/authMiddleware.js';
import { requirePermission } from '../middleware/roleMiddleware.js';

const router = express.Router();

// Helper function to format path
const formatPath = (path) => {
  if (!path || path === '#') return path;
  // Remove leading and trailing slashes, then add a single leading slash
  return '/' + path.replace(/^\/+|\/+$/g, '');
};

// --- GET NAVIGATION LINKS ---
// This single endpoint handles multiple use cases based on query parameters.
router.get('/', async (req, res) => {
  try {
    const { flat, path } = req.query;

    // CASE 1: Fetch a single link by its path (e.g., for getting a page title)
    if (path) {
      const formattedPath = formatPath(path);
      const link = await NavLink.findOne({ path: formattedPath });
      if (!link) {
        // It's okay if a link isn't found, just return null.
        // The frontend can decide what to do (e.g., use a default title).
        return res.status(200).json(null);
      }
      return res.json(link);
    }

    // --- Default behavior: Fetch all links ---
    const allLinks = await NavLink.find({}).sort('order'); // Sort by order for future drag-drop

    // CASE 2: Fetch a flat list of all links (e.g., for dashboard dropdowns)
    if (flat) {
      return res.json(allLinks);
    }

    // CASE 3 (Default): Fetch links structured in a hierarchy (for Navbar display)
    const linkMap = {};
    const topLevelLinks = [];

    allLinks.forEach(link => {
      // Initialize each link with a children array
      linkMap[link._id] = { ...link.toObject(), children: [] };
    });

    allLinks.forEach(link => {
      // If a link has a parent, push it into the parent's children array
      if (link.parentId && linkMap[link.parentId]) {
        linkMap[link.parentId].children.push(linkMap[link._id]);
      } else {
        // Otherwise, it's a top-level link
        topLevelLinks.push(linkMap[link._id]);
      }
    });

    res.json(topLevelLinks);

  } catch (error) {
    console.error("Error fetching nav links:", error);
    res.status(500).json({ message: 'Server Error: Could not fetch navigation links.' });
  }
});


// --- CREATE A NEW NAVIGATION LINK ---
router.post('/', protect, requirePermission('navbar'), async (req, res) => {
  const { title, path, parentId, isDropdown } = req.body;

  // Server-side validation
  if (!title) {
    return res.status(400).json({ message: 'Title is a required field.' });
  }
  // A regular link must have a path. A dropdown container should not.
  if (!isDropdown && (!path || path.trim() === '')) {
    return res.status(400).json({ message: 'A valid path (e.g., /my-page) is required for a regular link.' });
  }

  const newLinkData = {
    title,
    isDropdown,
    parentId: parentId || null,
    // Set path to '#' for dropdown containers, otherwise use the provided path
    path: isDropdown ? '#' : formatPath(path),
  };

  try {
    const newLink = new NavLink(newLinkData);
    const savedLink = await newLink.save();
    res.status(201).json(savedLink);
  } catch (error) {
    res.status(400).json({ message: 'Error creating link.', error: error.message });
  }
});


// --- UPDATE AN EXISTING NAVIGATION LINK ---
router.patch('/:id', protect, requirePermission('navbar'), async (req, res) => {
  const { title, path, parentId, isDropdown } = req.body;

  try {
    const linkToUpdate = await NavLink.findById(req.params.id);
    if (!linkToUpdate) {
        return res.status(404).json({ message: 'Link not found' });
    }
    
    // Apply updates from the request body
    linkToUpdate.title = title;
    linkToUpdate.isDropdown = isDropdown;
    linkToUpdate.parentId = parentId || null;
    linkToUpdate.path = isDropdown ? '#' : formatPath(path); // Update path based on dropdown status

    const updatedLink = await linkToUpdate.save();
    res.json(updatedLink);
  } catch (error) {
    res.status(400).json({ message: 'Error updating link.', error: error.message });
  }
});


// --- DELETE A NAVIGATION LINK (AND ALL ITS CHILDREN) ---
router.delete('/:id', protect, requirePermission('navbar'), async (req, res) => {
  try {
    const linkIdToDelete = req.params.id;
    const allChildrenIds = [];
    
    // Use a queue to find all descendants (children, grandchildren, etc.)
    const queue = [linkIdToDelete];
    
    while(queue.length > 0){
        const currentId = queue.shift();
        const children = await NavLink.find({ parentId: currentId });
        children.forEach(child => {
            allChildrenIds.push(child._id);
            queue.push(child._id);
        });
    }

    // Combine the initial link and all its descendants into one array for deletion
    const idsToDelete = [linkIdToDelete, ...allChildrenIds];

    await NavLink.deleteMany({ _id: { $in: idsToDelete } });
    
    res.json({ message: 'Link and all sub-links deleted successfully' });
  } catch (error) {
    console.error("Error deleting link:", error);
    res.status(500).json({ message: 'Error deleting link and its children.' });
  }
});

export default router;