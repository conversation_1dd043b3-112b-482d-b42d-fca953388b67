import React from 'react';
import { Plus } from 'lucide-react';

const CategoryForm = ({ 
  newCategoryName, 
  setNewCategoryName, 
  imageUrl, 
  setImageUrl, 
  selectedNavLink, 
  setSelectedNavLink, 
  navLinks, 
  onSubmit, 
  formLoading 
}) => {
  return (
    <div className="bg-white rounded-xl shadow-lg p-6 sticky top-6">
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <Plus size={20} className="mr-2" />
        New Category
      </h2>
      <form onSubmit={onSubmit} className="space-y-4">
        <div>
          <label htmlFor="categoryName" className="block text-sm font-medium text-gray-700">
            Category Name
          </label>
          <input
            type="text"
            id="categoryName"
            value={newCategoryName}
            onChange={(e) => setNewCategoryName(e.target.value)}
            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            required
            placeholder="e.g. Gaming Laptops"
          />
        </div>

        <div>
          <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700">
            Image URL (optional)
          </label>
          <input
            type="url"
            id="imageUrl"
            value={imageUrl}
            onChange={(e) => setImageUrl(e.target.value)}
            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            placeholder="https://example.com/image.jpg"
          />
        </div>

        <div>
          <label htmlFor="navLink" className="block text-sm font-medium text-gray-700">
            Associated Page (optional)
          </label>
          <select
            id="navLink"
            value={selectedNavLink}
            onChange={(e) => setSelectedNavLink(e.target.value)}
            className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          >
            <option value="">None</option>
            {navLinks.map(link => (
              <option key={link._id} value={link._id}>
                {link.name || link.title}
              </option>
            ))}
          </select>
        </div>

        <button
          type="submit"
          disabled={formLoading}
          className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 focus:ring-4 focus:ring-indigo-200 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {formLoading ? 'Creating...' : 'Create Category'}
        </button>
      </form>
    </div>
  );
};

export default CategoryForm;
