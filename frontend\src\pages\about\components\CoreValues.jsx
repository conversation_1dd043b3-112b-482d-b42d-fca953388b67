import React, { useState } from 'react';

const CoreValues = ({ coreValuesData }) => {
  const [activePanel, setActivePanel] = useState('vision');

  // Use default values if coreValuesData is not provided
  const defaultCoreValues = {
    vision: { title: "Our Vision", description: "", points: [], image: "" },
    mission: { title: "Our Mission", description: "", points: [], image: "" },
    story: { title: "Our Story", description: "", points: [], image: "" }
  };

  const panelContent = coreValuesData || defaultCoreValues;

  return (
    <div className="py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Core Values</h2>
          <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-indigo-500 mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            At the heart of everything we do are three fundamental principles that guide our decisions and actions.
          </p>
        </div>

        {/* Tab Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-16">
          {['vision', 'mission', 'story'].map((panel) => (
            <button
              key={panel}
              onClick={() => setActivePanel(panel)}
              className={`px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 ${
                activePanel === panel
                  ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {panelContent[panel]?.title || panel.charAt(0).toUpperCase() + panel.slice(1)}
            </button>
          ))}
        </div>

        {/* Panel Content */}
        <div className="bg-white rounded-3xl shadow-xl overflow-hidden">
          <div className="md:flex">
            <div className="md:w-1/2 p-10">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                {panelContent[activePanel]?.title || activePanel.charAt(0).toUpperCase() + activePanel.slice(1)}
              </h3>

              <p className="text-xl text-gray-700 mb-8">
                {panelContent[activePanel]?.description || "No description available."}
              </p>

              {panelContent[activePanel]?.points && panelContent[activePanel].points.length > 0 && (
                <ul className="space-y-4 mb-8">
                  {panelContent[activePanel].points.map((point, index) => (
                    <li key={index} className="flex items-start">
                      <div className="bg-purple-100 rounded-full p-2 mr-3 mt-1">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-600" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-gray-700">{point}</span>
                    </li>
                  ))}
                </ul>
              )}

              <button className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:from-purple-700 hover:to-indigo-700 transition-all shadow-md">
                Learn More
              </button>
            </div>

            <div className="md:w-1/2 h-96 md:h-auto relative">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-indigo-500/10"></div>
              {panelContent[activePanel]?.image ? (
                <div
                  className="absolute inset-0 bg-cover bg-center"
                  style={{ backgroundImage: `url('${panelContent[activePanel].image}')` }}
                ></div>
              ) : (
                <div className={`absolute inset-0 bg-cover bg-center ${
                  activePanel === 'vision'
                    ? 'bg-[url("https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80")]'
                    : activePanel === 'mission'
                    ? 'bg-[url("https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80")]'
                    : 'bg-[url("https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80")]'
                }`}></div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoreValues;