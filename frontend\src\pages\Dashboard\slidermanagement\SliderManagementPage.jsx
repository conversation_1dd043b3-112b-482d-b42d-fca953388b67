import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Eye, EyeOff } from 'lucide-react';
import axios from 'axios';
import SlideModal from '../components/SlideModal';
import RemoveModal from '../../../hooks/RemoveModal';

const SliderManagementPage = () => {
  const [slides, setSlides] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSlide, setSelectedSlide] = useState(null);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
  const [slideToDelete, setSlideToDelete] = useState(null);
  // Using relative URL since axios is configured globally

  useEffect(() => {
    fetchSlides();
  }, []);

  const fetchSlides = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get('/api/slides/all');
      setSlides(response.data);
    } catch (err) {
      setError('Failed to fetch slides');
      console.error('Error fetching slides:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenModal = (slide = null) => {
    setSelectedSlide(slide);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedSlide(null);
  };

  // This function is passed to the modal
  const handleSaveSlide = async (slideData) => {
    const isEditing = !!selectedSlide;

    try {
      let response;
      if (isEditing) {
        response = await axios.patch(`/api/slides/${selectedSlide._id}`, slideData);
      } else {
        response = await axios.post('/api/slides', slideData);
      }

      fetchSlides(); // Refresh the list
      handleCloseModal(); // Close the modal on success
      return { success: true }; // Signal success to the modal
    } catch (err) {
      console.error('Failed to save slide:', err);
      // Return error to be displayed in the modal
      return { success: false, message: err.response?.data?.message || err.message };
    }
  };

  const handleDeleteSlide = async (slideId) => {
    setSlideToDelete(slideId);
    setIsRemoveModalOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await axios.delete(`/api/slides/${slideToDelete}`);
      fetchSlides();
      setIsRemoveModalOpen(false);
      setSlideToDelete(null);
    } catch (err) {
      setError('Failed to delete slide.');
      console.error('Error deleting slide:', err);
    }
  };

  const handleToggleActive = async (slide) => {
    try {
      await axios.patch(`/api/slides/${slide._id}`, { isActive: !slide.isActive });
      fetchSlides();
    } catch (err) {
      setError('Failed to update slide status.');
      console.error('Error toggling slide status:', err);
    }
  };

  if (loading) return <div className="text-center p-10">Loading slides...</div>;
  if (error && slides.length === 0) return <div className="text-red-500 p-4">{error}</div>;

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Slider Management</h1>
        <button
          onClick={() => handleOpenModal()}
          className="bg-indigo-600 text-white py-2 px-4 rounded-lg flex items-center gap-2 hover:bg-indigo-700 transition"
        >
          <Plus size={20} /> Add New Slide
        </button>
      </div>
      
      {error && <div className="text-red-500 bg-red-50 p-3 rounded-md mb-4">{error}</div>}

      <div className="bg-white p-6 rounded-xl shadow-md">
        <div className="space-y-4">
          {slides.map((slide, index) => (
            <div key={slide._id} className={`flex items-center gap-4 p-4 rounded-lg border ${slide.isActive ? 'bg-white' : 'bg-gray-100 opacity-60'}`}>
              <span className="font-bold text-gray-500">{index + 1}</span>
              <img src={slide.imageData} alt={slide.title} className="w-40 h-20 object-cover rounded-md bg-gray-200" />
              <div className="flex-grow">
                <h3 className="font-semibold text-lg">{slide.title}</h3>
                <p className="text-sm text-gray-500">{slide.subtitle}</p>
                <a href={slide.buttonLink} target="_blank" rel="noopener noreferrer" className="text-xs text-indigo-500 hover:underline">{slide.buttonLink}</a>
              </div>
              <div className="flex items-center gap-3">
                <button onClick={() => handleToggleActive(slide)} className="p-2 text-gray-500 hover:text-blue-600" title={slide.isActive ? 'Deactivate' : 'Activate'}>
                  {slide.isActive ? <Eye size={20}/> : <EyeOff size={20} />}
                </button>
                <button onClick={() => handleOpenModal(slide)} className="p-2 text-gray-500 hover:text-green-600" title="Edit">
                  <Edit size={20} />
                </button>
                <button onClick={() => handleDeleteSlide(slide._id)} className="p-2 text-gray-500 hover:text-red-600" title="Delete">
                  <Trash2 size={20} />
                </button>
              </div>
            </div>
          ))}
        </div>
        {slides.length === 0 && !loading && <p className="text-center text-gray-500 py-8">No slides found. Click "Add New Slide" to begin.</p>}
      </div>

      {/* The modal's logic is now more robust */}
      <SlideModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveSlide}
        slide={selectedSlide}
      />
      <RemoveModal 
        isOpen={isRemoveModalOpen}
        onClose={() => {
          setIsRemoveModalOpen(false);
          setSlideToDelete(null);
        }}
        onConfirm={confirmDelete}
        itemName="slide"
      />
    </div>
  );
};

export default SliderManagementPage;
