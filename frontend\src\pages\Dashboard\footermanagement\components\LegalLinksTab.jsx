import React from 'react';
import { Plus, Trash2 } from 'lucide-react';

const LegalLinksTab = ({ 
  footerData, 
  addLegalLink, 
  updateLegalLink, 
  removeLegalLink 
}) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Legal Links</h2>
        <button
          onClick={addLegalLink}
          className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          <Plus size={16} />
          Add Legal Link
        </button>
      </div>

      <div className="space-y-4">
        {footerData?.legalLinks?.map((link, index) => (
          <div key={index} className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg">
            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                type="text"
                value={link.text}
                onChange={(e) => updateLegalLink(index, 'text', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Link text"
              />
              <input
                type="text"
                value={link.url}
                onChange={(e) => updateLegalLink(index, 'url', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Link URL"
              />
            </div>
            <button
              onClick={() => removeLegalLink(index)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
            >
              <Trash2 size={16} />
            </button>
          </div>
        ))}

        {(!footerData?.legalLinks || footerData.legalLinks.length === 0) && (
          <div className="text-center py-8 text-gray-500">
            No legal links added yet. Click "Add Legal Link" to get started.
          </div>
        )}
      </div>
    </div>
  );
};

export default LegalLinksTab;
