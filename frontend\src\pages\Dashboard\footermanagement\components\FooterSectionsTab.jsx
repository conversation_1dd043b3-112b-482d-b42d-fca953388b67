import React from 'react';
import { Plus, Trash2, X } from 'lucide-react';

const FooterSectionsTab = ({ 
  footerData, 
  addSection, 
  updateSection, 
  removeSection,
  addLinkToSection,
  updateSectionLink,
  removeSectionLink
}) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Footer Sections</h2>
        <button
          onClick={addSection}
          className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          <Plus size={16} />
          Add Section
        </button>
      </div>

      <div className="space-y-6">
        {footerData?.sections?.map((section, sectionIndex) => (
          <div key={sectionIndex} className="border border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                <input
                  type="text"
                  value={section.title}
                  onChange={(e) => updateSection(sectionIndex, 'title', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Section title"
                />
                <input
                  type="number"
                  value={section.order}
                  onChange={(e) => updateSection(sectionIndex, 'order', parseInt(e.target.value))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Order"
                />
              </div>
              <button
                onClick={() => removeSection(sectionIndex)}
                className="ml-4 p-2 text-red-600 hover:bg-red-50 rounded-lg"
              >
                <Trash2 size={16} />
              </button>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <h4 className="font-medium text-gray-700">Links</h4>
                <button
                  onClick={() => addLinkToSection(sectionIndex)}
                  className="flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                >
                  <Plus size={14} />
                  Add Link
                </button>
              </div>

              {section.links?.map((link, linkIndex) => (
                <div key={linkIndex} className="flex items-center gap-3 p-3 bg-gray-50 rounded">
                  <input
                    type="text"
                    value={link.text}
                    onChange={(e) => updateSectionLink(sectionIndex, linkIndex, 'text', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Link text"
                  />
                  <input
                    type="text"
                    value={link.url}
                    onChange={(e) => updateSectionLink(sectionIndex, linkIndex, 'url', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Link URL"
                  />
                  <button
                    onClick={() => removeSectionLink(sectionIndex, linkIndex)}
                    className="p-2 text-red-600 hover:bg-red-100 rounded"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))}
            </div>
          </div>
        ))}

        {(!footerData?.sections || footerData.sections.length === 0) && (
          <div className="text-center py-8 text-gray-500">
            No footer sections added yet. Click "Add Section" to get started.
          </div>
        )}
      </div>
    </div>
  );
};

export default FooterSectionsTab;
