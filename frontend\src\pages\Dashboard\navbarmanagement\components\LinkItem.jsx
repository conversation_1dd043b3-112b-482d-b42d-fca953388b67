import React from 'react';
import { Edit, Trash2, GripVertical, Package, Link as LinkIcon } from 'lucide-react';

const LinkItem = ({ link, level = 0, onEdit, onDelete, onAddSubItem }) => (
  <div className="rounded-lg bg-white border border-gray-200">
    <div className="flex items-center justify-between p-3">
      <div className="flex items-center gap-3">
        <GripVertical className="cursor-grab text-gray-400" size={18} />
        {link.isDropdown ? 
          <Package size={18} className="text-indigo-500" /> : 
          <LinkIcon size={18} className="text-gray-500" />
        }
        <div>
          <p className="font-semibold">{link.title}</p>
          {!link.isDropdown && <p className="text-sm text-gray-500">{link.path}</p>}
        </div>
      </div>
      <div className="flex items-center gap-1">
        {link.isDropdown && (
          <button 
            onClick={() => onAddSubItem(link._id)} 
            className="p-2 text-xs font-semibold text-white bg-indigo-500 hover:bg-indigo-600 rounded-md"
          >
            Add Sub-item
          </button>
        )}
        <button 
          onClick={() => onEdit(link)} 
          className="p-2 text-gray-500 hover:text-blue-600" 
          title="Edit"
        >
          <Edit size={16} />
        </button>
        <button 
          onClick={() => onDelete(link._id)} 
          className="p-2 text-gray-500 hover:text-red-600" 
          title="Delete"
        >
          <Trash2 size={16} />
        </button>
      </div>
    </div>
  </div>
);

export default LinkItem;
