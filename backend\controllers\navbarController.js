import Navbar from '../models/Navbar.js';

// @desc    Get navbar configuration
// @route   GET /api/navbar/config
// @access  Public
export const getNavbarConfig = async (req, res) => {
  try {
    let navbar = await Navbar.findOne({ isActive: true });
    
    // If no navbar config exists, create a default one
    if (!navbar) {
      navbar = new Navbar({
        brandInfo: {
          companyName: 'Shopify',
          logo: ''
        },
        isActive: true
      });
      await navbar.save();
    }
    
    res.json(navbar);
  } catch (error) {
    console.error('Error fetching navbar config:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update navbar configuration
// @route   PUT /api/navbar/config
// @access  Private (Admin/Creator)
export const updateNavbarConfig = async (req, res) => {
  try {
    const { brandInfo } = req.body;
    
    // Validate required fields
    if (!brandInfo || !brandInfo.companyName) {
      return res.status(400).json({ message: 'Company name is required' });
    }
    
    let navbar = await Navbar.findOne({ isActive: true });
    
    if (navbar) {
      // Update existing navbar config
      navbar.brandInfo = {
        companyName: brandInfo.companyName,
        logo: brandInfo.logo || navbar.brandInfo.logo
      };
      await navbar.save();
    } else {
      // Create new navbar config
      navbar = new Navbar({
        brandInfo: {
          companyName: brandInfo.companyName,
          logo: brandInfo.logo || ''
        },
        isActive: true
      });
      await navbar.save();
    }
    
    res.json(navbar);
  } catch (error) {
    console.error('Error updating navbar config:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
